'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

/**
 * @description Contains functions to select the video codec profile.
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/video_manipulation_and_delivery#video_codec_settings|Video codec settings}
 * @memberOf Qualifiers
 * @namespace VideoCodecProfile
 * @see Visit {@link Actions.Transcode|Transcode} for an example
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecProfile
 * @return {string}
 */
function high() {
    return 'high';
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecProfile
 * @return {string}
 */
function main() {
    return 'main';
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecProfile
 * @return {string}
 */
function baseline() {
    return 'baseline';
}
const VideoCodecProfile = {
    baseline,
    main,
    high
};

exports.VideoCodecProfile = VideoCodecProfile;
exports.baseline = baseline;
exports.high = high;
exports.main = main;
