import { Transport, TransportRequestOptions, TransportRequestOptionsWithMeta, TransportRequestOptionsWithOutMeta, TransportResult } from '@elastic/transport';
import * as T from '../types';
interface That {
    transport: Transport;
}
/**
  * Performs a kNN search.
  * @see {@link https://www.elastic.co/guide/en/elasticsearch/reference/9.0/search-search.html | Elasticsearch API documentation}
  */
export default function KnnSearchApi(this: That, params?: T.TODO, options?: TransportRequestOptionsWithOutMeta): Promise<T.TODO>;
export default function KnnSearchApi(this: That, params?: T.TODO, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.TODO, unknown>>;
export default function KnnSearchApi(this: That, params?: T.TODO, options?: TransportRequestOptions): Promise<T.TODO>;
export {};
