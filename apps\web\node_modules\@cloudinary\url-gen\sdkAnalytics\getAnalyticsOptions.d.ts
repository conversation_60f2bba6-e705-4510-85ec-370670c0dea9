import { IAnalyticsOptions } from "./interfaces/IAnalyticsOptions.js";
import { ITrackedPropertiesThroughAnalytics } from "./interfaces/ITrackedPropertiesThroughAnalytics.js";
/**
 * @private
 * @description Gets the analyticsOptions from options- should include sdkSemver, techVersion, sdkCode, and feature
 * @param {ITrackedPropertiesThroughAnalytics} options
 * @returns {IAnalyticsOptions}
 */
export declare function getAnalyticsOptions(options: ITrackedPropertiesThroughAnalytics): IAnalyticsOptions;
