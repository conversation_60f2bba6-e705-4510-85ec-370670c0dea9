import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

// Import components
import RestaurantCard from '../components/RestaurantCard';
import CategoryFilter from '../components/CategoryFilter';
import SearchBar from '../components/SearchBar';
import MapSection from '../components/MapSection';
import MobileHeader from '../components/MobileHeader';
import FooterNavigation from '../components/FooterNavigation';
import { ResponsiveContainer, ResponsiveCard, ResponsiveText, ResponsiveGrid } from '../components/ResponsiveContainer';
import { useResponsive } from '../utils/responsive';
import { useCart } from '../contexts/CartContext';

// Temporary types until shared-types is working
interface Restaurant {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  cuisine: string[];
  rating?: number;
  deliveryTime?: string;
  deliveryFee?: number;
  minimumOrder?: number;
  distance?: number;
  isOpen: boolean;
}

interface Category {
  id: string;
  name: string;
  image?: string;
  description?: string;
  featured?: boolean;
}

export default function HomeScreen({ navigation }: any) {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showMap, setShowMap] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<{
    lat: number;
    lng: number;
    address: string;
  } | null>(null);

  // Get responsive screen information
  const { isTablet, deviceType } = useResponsive();

  // Cart functionality
  const { addToCart, getCartItemCount } = useCart();
  const cartItemCount = getCartItemCount();

  // Load data function (same logic as web app)
  const loadData = async () => {
    try {
      // TODO: Implement Firebase data loading
      // This will use the same business logic as your web app
      console.log('Loading restaurants and categories...');
      
      // Placeholder data for now
      setRestaurants([]);
      setCategories([]);
    } catch (error) {
      console.error('Error loading data:', error);
      setRestaurants([]);
      setCategories([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Filter restaurants (same logic as web app)
  const filteredRestaurants = restaurants.filter(restaurant => {
    const matchesSearch = !searchQuery ||
      restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      restaurant.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      restaurant.cuisine.some(c => c.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = !selectedCategory ||
      restaurant.cuisine.includes(selectedCategory);

    return matchesSearch && matchesCategory;
  });

  // Handle refresh
  const onRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  // Handle location select
  const handleLocationSelect = (location: {lat: number; lng: number; address: string}) => {
    setSelectedLocation(location);
    console.log('Location selected:', location);
  };

  // Demo function to add a test item
  const addTestItem = () => {
    const testItem = {
      id: `test-${Date.now()}`,
      name: 'Delicious Pizza',
      description: 'A mouth-watering pizza to test the cart',
      price: 15.99,
      image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
      restaurantId: 'test-restaurant',
      category: 'Pizza',
      available: true,
    };
    addToCart(testItem, 1);
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Brand color status bar area only */}
      <SafeAreaView style={{ backgroundColor: '#f3a823' }} edges={['top']} />

      {/* Mobile Header - Now responsive for tablets */}
      <MobileHeader
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onNotificationPress={() => {
          navigation.navigate('Notifications');
        }}
        onWishlistPress={() => {
          navigation.navigate('Wishlist');
        }}
      />

      {/* Content area with white/light background */}
      <View style={{ flex: 1, backgroundColor: '#f9fafb' }}>
        <ScrollView
          className="flex-1"
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >

        {/* Hero Banner */}
        <View style={{ paddingHorizontal: 16, paddingTop: 16 }}>
          <View style={{
            backgroundColor: '#f3a823',
            borderRadius: 16,
            padding: 20,
            marginBottom: 24,
          }}>
            <Text style={{ color: 'white', fontSize: 24, fontWeight: 'bold', marginBottom: 8 }}>
              Free delivery on orders over $30
            </Text>
            <Text style={{ color: 'white', opacity: 0.9, fontSize: 16 }}>
              Limited time offer • Use code FREEDEL30
            </Text>
          </View>
        </View>

        {/* Cuisines Carousel */}
        <View style={{ marginBottom: 32 }}>
          <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
            <Text style={{ fontSize: 22, fontWeight: 'bold', color: '#111827' }}>
              What are you craving?
            </Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}
          >
            {[
              { name: 'Pizza', emoji: '🍕', color: '#ef4444' },
              { name: 'Burgers', emoji: '🍔', color: '#f3a823' },
              { name: 'Sushi', emoji: '🍣', color: '#06b6d4' },
              { name: 'Chinese', emoji: '🥡', color: '#eab308' },
              { name: 'Mexican', emoji: '🌮', color: '#10b981' },
              { name: 'Italian', emoji: '🍝', color: '#8b5cf6' },
              { name: 'Indian', emoji: '🍛', color: '#f59e0b' },
              { name: 'Thai', emoji: '🍜', color: '#ec4899' },
              { name: 'Desserts', emoji: '🍰', color: '#84cc16' },
              { name: 'Coffee', emoji: '☕', color: '#6b7280' },
            ].map((cuisine, index) => (
              <TouchableOpacity
                key={index}
                style={{
                  alignItems: 'center',
                  marginRight: 16,
                  width: 80,
                }}
              >
                <View style={{
                  width: 64,
                  height: 64,
                  backgroundColor: `${cuisine.color}15`,
                  borderRadius: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: 8,
                  borderWidth: 2,
                  borderColor: `${cuisine.color}30`,
                }}>
                  <Text style={{ fontSize: 28 }}>{cuisine.emoji}</Text>
                </View>
                <Text style={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: '#374151',
                  textAlign: 'center',
                }}>
                  {cuisine.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Featured Restaurants Carousel */}
        <View style={{ marginBottom: 32 }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 16,
            marginBottom: 16
          }}>
            <Text style={{ fontSize: 22, fontWeight: 'bold', color: '#111827' }}>
              Featured restaurants
            </Text>
            <TouchableOpacity>
              <Text style={{ color: '#f3a823', fontWeight: '600' }}>See all</Text>
            </TouchableOpacity>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}
          >
            {[
              {
                name: 'McDonald\'s',
                cuisine: 'Fast Food • American',
                rating: 4.3,
                deliveryTime: '15-25',
                deliveryFee: 'Free',
                image: '🍟',
                promo: '20% off',
                bgColor: '#fef3c7',
              },
              {
                name: 'Pizza Hut',
                cuisine: 'Pizza • Italian',
                rating: 4.5,
                deliveryTime: '25-35',
                deliveryFee: '$2.99',
                image: '🍕',
                promo: 'Buy 1 Get 1',
                bgColor: '#fecaca',
              },
              {
                name: 'Starbucks',
                cuisine: 'Coffee • Cafe',
                rating: 4.7,
                deliveryTime: '10-20',
                deliveryFee: 'Free',
                image: '☕',
                promo: '30% off drinks',
                bgColor: '#d1fae5',
              },
              {
                name: 'KFC',
                cuisine: 'Chicken • Fast Food',
                rating: 4.2,
                deliveryTime: '20-30',
                deliveryFee: 'Free',
                image: '🍗',
                promo: 'Family Feast',
                bgColor: '#fef3e2',
              },
              {
                name: 'Subway',
                cuisine: 'Sandwiches • Healthy',
                rating: 4.4,
                deliveryTime: '15-25',
                deliveryFee: '$1.99',
                image: '🥪',
                promo: 'Sub of the day',
                bgColor: '#e0f2fe',
              },
            ].map((restaurant, index) => (
              <TouchableOpacity
                key={index}
                style={{
                  width: 280,
                  marginRight: 16,
                  backgroundColor: 'white',
                  borderRadius: 16,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 8,
                  elevation: 4,
                }}
              >
                {/* Restaurant Image */}
                <View style={{
                  height: 140,
                  backgroundColor: restaurant.bgColor,
                  borderTopLeftRadius: 16,
                  borderTopRightRadius: 16,
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                }}>
                  <Text style={{ fontSize: 48 }}>{restaurant.image}</Text>
                  {restaurant.promo && (
                    <View style={{
                      position: 'absolute',
                      top: 12,
                      left: 12,
                      backgroundColor: '#f3a823',
                      paddingHorizontal: 8,
                      paddingVertical: 4,
                      borderRadius: 8,
                    }}>
                      <Text style={{ color: 'white', fontSize: 12, fontWeight: '600' }}>
                        {restaurant.promo}
                      </Text>
                    </View>
                  )}
                </View>

                {/* Restaurant Info */}
                <View style={{ padding: 16 }}>
                  <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 4 }}>
                    {restaurant.name}
                  </Text>
                  <Text style={{ fontSize: 14, color: '#6b7280', marginBottom: 8 }}>
                    {restaurant.cuisine}
                  </Text>

                  <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Ionicons name="star" size={16} color="#fbbf24" />
                      <Text style={{ marginLeft: 4, fontSize: 14, fontWeight: '600' }}>
                        {restaurant.rating}
                      </Text>
                    </View>

                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Ionicons name="time-outline" size={16} color="#6b7280" />
                      <Text style={{ marginLeft: 4, fontSize: 14, color: '#6b7280' }}>
                        {restaurant.deliveryTime} min
                      </Text>
                    </View>

                    <Text style={{
                      fontSize: 14,
                      color: restaurant.deliveryFee === 'Free' ? '#10b981' : '#6b7280',
                      fontWeight: restaurant.deliveryFee === 'Free' ? '600' : 'normal'
                    }}>
                      {restaurant.deliveryFee === 'Free' ? 'Free delivery' : restaurant.deliveryFee + ' delivery'}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Deals & Offers */}
        <View style={{ marginBottom: 32 }}>
          <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
            <Text style={{ fontSize: 22, fontWeight: 'bold', color: '#111827' }}>
              Deals & Offers
            </Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}
          >
            {[
              {
                title: '50% OFF',
                subtitle: 'First order',
                description: 'Use code WELCOME50',
                bgColor: '#fef3e2',
                textColor: '#92400e',
                icon: '🎉',
              },
              {
                title: 'Free Delivery',
                subtitle: 'Orders over $25',
                description: 'No minimum for premium',
                bgColor: '#d1fae5',
                textColor: '#065f46',
                icon: '🚚',
              },
              {
                title: 'Buy 1 Get 1',
                subtitle: 'Pizza deals',
                description: 'Selected restaurants',
                bgColor: '#fecaca',
                textColor: '#991b1b',
                icon: '🍕',
              },
            ].map((deal, index) => (
              <TouchableOpacity
                key={index}
                style={{
                  width: 200,
                  marginRight: 16,
                  backgroundColor: deal.bgColor,
                  borderRadius: 16,
                  padding: 20,
                }}
              >
                <Text style={{ fontSize: 32, marginBottom: 8 }}>{deal.icon}</Text>
                <Text style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: deal.textColor,
                  marginBottom: 4
                }}>
                  {deal.title}
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: deal.textColor,
                  marginBottom: 4,
                  opacity: 0.8
                }}>
                  {deal.subtitle}
                </Text>
                <Text style={{
                  fontSize: 12,
                  color: deal.textColor,
                  opacity: 0.7
                }}>
                  {deal.description}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Popular Near You */}
        <View style={{ marginBottom: 32 }}>
          <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
            <Text style={{ fontSize: 22, fontWeight: 'bold', color: '#111827' }}>
              Popular near you
            </Text>
          </View>
          <View style={{ paddingHorizontal: 16 }}>
            {[
              {
                name: 'Domino\'s Pizza',
                cuisine: 'Pizza • Fast Food',
                rating: 4.4,
                deliveryTime: '20-30',
                deliveryFee: 'Free',
                image: '🍕',
                distance: '0.8 km',
                popular: true,
              },
              {
                name: 'Burger King',
                cuisine: 'Burgers • Fast Food',
                rating: 4.2,
                deliveryTime: '15-25',
                deliveryFee: '$1.99',
                image: '🍔',
                distance: '1.2 km',
                popular: false,
              },
              {
                name: 'Taco Bell',
                cuisine: 'Mexican • Fast Food',
                rating: 4.3,
                deliveryTime: '18-28',
                deliveryFee: 'Free',
                image: '🌮',
                distance: '0.5 km',
                popular: true,
              },
            ].map((restaurant, index) => (
              <TouchableOpacity
                key={index}
                style={{
                  backgroundColor: 'white',
                  borderRadius: 16,
                  padding: 16,
                  marginBottom: 12,
                  flexDirection: 'row',
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 1 },
                  shadowOpacity: 0.05,
                  shadowRadius: 4,
                  elevation: 2,
                }}
              >
                <View style={{
                  width: 60,
                  height: 60,
                  backgroundColor: '#f3f4f6',
                  borderRadius: 12,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 16,
                }}>
                  <Text style={{ fontSize: 24 }}>{restaurant.image}</Text>
                </View>

                <View style={{ flex: 1 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
                    <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
                      {restaurant.name}
                    </Text>
                    {restaurant.popular && (
                      <View style={{
                        backgroundColor: '#f3a823',
                        paddingHorizontal: 6,
                        paddingVertical: 2,
                        borderRadius: 4,
                        marginLeft: 8,
                      }}>
                        <Text style={{ color: 'white', fontSize: 10, fontWeight: '600' }}>
                          Popular
                        </Text>
                      </View>
                    )}
                  </View>
                  <Text style={{ fontSize: 14, color: '#6b7280', marginBottom: 8 }}>
                    {restaurant.cuisine} • {restaurant.distance}
                  </Text>

                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 16 }}>
                      <Ionicons name="star" size={14} color="#fbbf24" />
                      <Text style={{ marginLeft: 4, fontSize: 14, fontWeight: '600' }}>
                        {restaurant.rating}
                      </Text>
                    </View>

                    <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 16 }}>
                      <Ionicons name="time-outline" size={14} color="#6b7280" />
                      <Text style={{ marginLeft: 4, fontSize: 14, color: '#6b7280' }}>
                        {restaurant.deliveryTime} min
                      </Text>
                    </View>

                    <Text style={{
                      fontSize: 14,
                      color: restaurant.deliveryFee === 'Free' ? '#10b981' : '#6b7280',
                      fontWeight: restaurant.deliveryFee === 'Free' ? '600' : 'normal'
                    }}>
                      {restaurant.deliveryFee === 'Free' ? 'Free delivery' : restaurant.deliveryFee}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Quick Picks */}
        <View style={{ marginBottom: 40 }}>
          <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
            <Text style={{ fontSize: 22, fontWeight: 'bold', color: '#111827' }}>
              Quick picks for you
            </Text>
            <Text style={{ fontSize: 14, color: '#6b7280', marginTop: 4 }}>
              Based on your recent orders
            </Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 16 }}
          >
            {[
              { name: 'Margherita Pizza', price: '$12.99', image: '🍕', restaurant: 'Pizza Palace' },
              { name: 'Chicken Burger', price: '$8.99', image: '🍔', restaurant: 'Burger Corner' },
              { name: 'Pad Thai', price: '$11.50', image: '🍜', restaurant: 'Thai Express' },
              { name: 'Caesar Salad', price: '$9.99', image: '🥗', restaurant: 'Green Garden' },
              { name: 'Chocolate Cake', price: '$6.99', image: '🍰', restaurant: 'Sweet Treats' },
            ].map((item, index) => (
              <TouchableOpacity
                key={index}
                style={{
                  width: 160,
                  marginRight: 12,
                  backgroundColor: 'white',
                  borderRadius: 12,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 1 },
                  shadowOpacity: 0.05,
                  shadowRadius: 4,
                  elevation: 2,
                }}
              >
                <View style={{
                  height: 100,
                  backgroundColor: '#f9fafb',
                  borderTopLeftRadius: 12,
                  borderTopRightRadius: 12,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <Text style={{ fontSize: 32 }}>{item.image}</Text>
                </View>

                <View style={{ padding: 12 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827', marginBottom: 4 }}>
                    {item.name}
                  </Text>
                  <Text style={{ fontSize: 12, color: '#6b7280', marginBottom: 8 }}>
                    {item.restaurant}
                  </Text>
                  <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#f3a823' }}>
                    {item.price}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        </ScrollView>
      </View>

      {/* Footer Navigation - positioned above bottom safe area */}
      <FooterNavigation navigation={navigation} activeScreen="Home" />

      {/* Bottom safe area with light background */}
      <SafeAreaView style={{ backgroundColor: '#f9fafb' }} edges={['bottom']} />
    </View>
  );
}


