"use strict";
/*
 * Copyright Elasticsearch B.V. and contributors
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = SearchMvtApi;
const commonQueryParams = ['error_trace', 'filter_path', 'human', 'pretty'];
const acceptedParams = {
    search_mvt: {
        path: [
            'index',
            'field',
            'zoom',
            'x',
            'y'
        ],
        body: [
            'aggs',
            'buffer',
            'exact_bounds',
            'extent',
            'fields',
            'grid_agg',
            'grid_precision',
            'grid_type',
            'query',
            'runtime_mappings',
            'size',
            'sort',
            'track_total_hits',
            'with_labels'
        ],
        query: [
            'exact_bounds',
            'extent',
            'grid_agg',
            'grid_precision',
            'grid_type',
            'size',
            'with_labels'
        ]
    }
};
async function SearchMvtApi(params, options) {
    const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = acceptedParams.search_mvt;
    const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
    const querystring = userQuery != null ? { ...userQuery } : {};
    let body;
    const userBody = params === null || params === void 0 ? void 0 : params.body;
    if (userBody != null) {
        if (typeof userBody === 'string') {
            body = userBody;
        }
        else {
            body = { ...userBody };
        }
    }
    for (const key in params) {
        if (acceptedBody.includes(key)) {
            body = body !== null && body !== void 0 ? body : {};
            // @ts-expect-error
            body[key] = params[key];
        }
        else if (acceptedPath.includes(key)) {
            continue;
        }
        else if (key !== 'body' && key !== 'querystring') {
            if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                // @ts-expect-error
                querystring[key] = params[key];
            }
            else {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
        }
    }
    const method = body != null ? 'POST' : 'GET';
    const path = `/${encodeURIComponent(params.index.toString())}/_mvt/${encodeURIComponent(params.field.toString())}/${encodeURIComponent(params.zoom.toString())}/${encodeURIComponent(params.x.toString())}/${encodeURIComponent(params.y.toString())}`;
    const meta = {
        name: 'search_mvt',
        pathParts: {
            index: params.index,
            field: params.field,
            zoom: params.zoom,
            x: params.x,
            y: params.y
        }
    };
    return await this.transport.request({ path, method, querystring, body, meta }, options);
}
//# sourceMappingURL=search_mvt.js.map