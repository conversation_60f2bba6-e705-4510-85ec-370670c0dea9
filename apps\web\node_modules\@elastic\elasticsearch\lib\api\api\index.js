"use strict";
/*
 * Copyright Elasticsearch B.V. and contributors
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = IndexApi;
const commonQueryParams = ['error_trace', 'filter_path', 'human', 'pretty'];
const acceptedParams = {
    index: {
        path: [
            'id',
            'index'
        ],
        body: [
            'document'
        ],
        query: [
            'if_primary_term',
            'if_seq_no',
            'include_source_on_error',
            'op_type',
            'pipeline',
            'refresh',
            'routing',
            'timeout',
            'version',
            'version_type',
            'wait_for_active_shards',
            'require_alias'
        ]
    }
};
async function IndexApi(params, options) {
    var _a;
    const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = acceptedParams.index;
    const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
    const querystring = userQuery != null ? { ...userQuery } : {};
    let body = (_a = params.body) !== null && _a !== void 0 ? _a : undefined;
    for (const key in params) {
        if (acceptedBody.includes(key)) {
            // @ts-expect-error
            body = params[key];
        }
        else if (acceptedPath.includes(key)) {
            continue;
        }
        else if (key !== 'body' && key !== 'querystring') {
            if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                // @ts-expect-error
                querystring[key] = params[key];
            }
            else {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
        }
    }
    let method = '';
    let path = '';
    if (params.index != null && params.id != null) {
        method = 'PUT';
        path = `/${encodeURIComponent(params.index.toString())}/_doc/${encodeURIComponent(params.id.toString())}`;
    }
    else {
        method = 'POST';
        path = `/${encodeURIComponent(params.index.toString())}/_doc`;
    }
    const meta = {
        name: 'index',
        pathParts: {
            id: params.id,
            index: params.index
        }
    };
    return await this.transport.request({ path, method, querystring, body, meta }, options);
}
//# sourceMappingURL=index.js.map