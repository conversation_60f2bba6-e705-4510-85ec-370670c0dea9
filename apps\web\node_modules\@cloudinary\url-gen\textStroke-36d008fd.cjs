'use strict';

/**
 * @description Contains function to set the outline stroke.
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/layers#text_layer_options|Adding text overlays to images}
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/video_layers#text_layer_options|Adding text overlays to videos}
 * @memberOf Qualifiers
 * @namespace TextAlignment
 * @see To be used with {@link Qualifiers.TextStyle|Text Style}
 */
/**
 * @summary qualifier Adding a Border-like qualifier to the same action.
 * @memberOf Qualifiers.Stroke
 * @param {number|string|ExpressionQualifier} width The width in pixels.
 * @param {number|string|SystemColors} color The color of the border.
 */
function solid(width, color) {
    return `bo_${width}px_solid_${color}`;
}
const Stroke = { solid };

exports.Stroke = Stroke;
exports.solid = solid;
