import { Transport, TransportRequestOptions, TransportRequestOptionsWithMeta, TransportRequestOptionsWithOutMeta, TransportResult } from '@elastic/transport';
import * as T from '../types';
interface That {
    transport: Transport;
    acceptedParams: Record<string, {
        path: string[];
        body: string[];
        query: string[];
    }>;
}
export default class Eql {
    transport: Transport;
    acceptedParams: Record<string, {
        path: string[];
        body: string[];
        query: string[];
    }>;
    constructor(transport: Transport);
    /**
      * Delete an async EQL search. Delete an async EQL search or a stored synchronous EQL search. The API also deletes results for the search.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/operation/operation-eql-delete | Elasticsearch API documentation}
      */
    delete(this: That, params: T.EqlDeleteRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.EqlDeleteResponse>;
    delete(this: That, params: T.EqlDeleteRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.EqlDeleteResponse, unknown>>;
    delete(this: That, params: T.EqlDeleteRequest, options?: TransportRequestOptions): Promise<T.EqlDeleteResponse>;
    /**
      * Get async EQL search results. Get the current status and available results for an async EQL search or a stored synchronous EQL search.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/operation/operation-eql-get | Elasticsearch API documentation}
      */
    get<TEvent = unknown>(this: That, params: T.EqlGetRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.EqlGetResponse<TEvent>>;
    get<TEvent = unknown>(this: That, params: T.EqlGetRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.EqlGetResponse<TEvent>, unknown>>;
    get<TEvent = unknown>(this: That, params: T.EqlGetRequest, options?: TransportRequestOptions): Promise<T.EqlGetResponse<TEvent>>;
    /**
      * Get the async EQL status. Get the current status for an async EQL search or a stored synchronous EQL search without returning results.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/operation/operation-eql-get-status | Elasticsearch API documentation}
      */
    getStatus(this: That, params: T.EqlGetStatusRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.EqlGetStatusResponse>;
    getStatus(this: That, params: T.EqlGetStatusRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.EqlGetStatusResponse, unknown>>;
    getStatus(this: That, params: T.EqlGetStatusRequest, options?: TransportRequestOptions): Promise<T.EqlGetStatusResponse>;
    /**
      * Get EQL search results. Returns search results for an Event Query Language (EQL) query. EQL assumes each document in a data stream or index corresponds to an event.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/operation/operation-eql-search | Elasticsearch API documentation}
      */
    search<TEvent = unknown>(this: That, params: T.EqlSearchRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.EqlSearchResponse<TEvent>>;
    search<TEvent = unknown>(this: That, params: T.EqlSearchRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.EqlSearchResponse<TEvent>, unknown>>;
    search<TEvent = unknown>(this: That, params: T.EqlSearchRequest, options?: TransportRequestOptions): Promise<T.EqlSearchResponse<TEvent>>;
}
export {};
