import { Transport, TransportRequestOptions, TransportRequestOptionsWithMeta, TransportRequestOptionsWithOutMeta, TransportResult } from '@elastic/transport';
import * as T from '../types';
interface That {
    transport: Transport;
    acceptedParams: Record<string, {
        path: string[];
        body: string[];
        query: string[];
    }>;
}
export default class Enrich {
    transport: Transport;
    acceptedParams: Record<string, {
        path: string[];
        body: string[];
        query: string[];
    }>;
    constructor(transport: Transport);
    /**
      * Delete an enrich policy. Deletes an existing enrich policy and its enrich index.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/operation/operation-enrich-delete-policy | Elasticsearch API documentation}
      */
    deletePolicy(this: That, params: T.EnrichDeletePolicyRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.EnrichDeletePolicyResponse>;
    deletePolicy(this: That, params: T.EnrichDeletePolicyRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.EnrichDeletePolicyResponse, unknown>>;
    deletePolicy(this: That, params: T.EnrichDeletePolicyRequest, options?: TransportRequestOptions): Promise<T.EnrichDeletePolicyResponse>;
    /**
      * Run an enrich policy. Create the enrich index for an existing enrich policy.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/operation/operation-enrich-execute-policy | Elasticsearch API documentation}
      */
    executePolicy(this: That, params: T.EnrichExecutePolicyRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.EnrichExecutePolicyResponse>;
    executePolicy(this: That, params: T.EnrichExecutePolicyRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.EnrichExecutePolicyResponse, unknown>>;
    executePolicy(this: That, params: T.EnrichExecutePolicyRequest, options?: TransportRequestOptions): Promise<T.EnrichExecutePolicyResponse>;
    /**
      * Get an enrich policy. Returns information about an enrich policy.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/operation/operation-enrich-get-policy | Elasticsearch API documentation}
      */
    getPolicy(this: That, params?: T.EnrichGetPolicyRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.EnrichGetPolicyResponse>;
    getPolicy(this: That, params?: T.EnrichGetPolicyRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.EnrichGetPolicyResponse, unknown>>;
    getPolicy(this: That, params?: T.EnrichGetPolicyRequest, options?: TransportRequestOptions): Promise<T.EnrichGetPolicyResponse>;
    /**
      * Create an enrich policy. Creates an enrich policy.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/operation/operation-enrich-put-policy | Elasticsearch API documentation}
      */
    putPolicy(this: That, params: T.EnrichPutPolicyRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.EnrichPutPolicyResponse>;
    putPolicy(this: That, params: T.EnrichPutPolicyRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.EnrichPutPolicyResponse, unknown>>;
    putPolicy(this: That, params: T.EnrichPutPolicyRequest, options?: TransportRequestOptions): Promise<T.EnrichPutPolicyResponse>;
    /**
      * Get enrich stats. Returns enrich coordinator statistics and information about enrich policies that are currently executing.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/operation/operation-enrich-stats | Elasticsearch API documentation}
      */
    stats(this: That, params?: T.EnrichStatsRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.EnrichStatsResponse>;
    stats(this: That, params?: T.EnrichStatsRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.EnrichStatsResponse, unknown>>;
    stats(this: That, params?: T.EnrichStatsRequest, options?: TransportRequestOptions): Promise<T.EnrichStatsResponse>;
}
export {};
