'use strict';

/**
 * @description Contains functions to select the type of color-blind condition to simulate.
 * <b>Learn more</b>: {@link https://cloudinary.com/blog/open_your_eyes_to_color_accessibility|Blog: Open your Eyes to Color Accessibility}
 * @memberOf Qualifiers
 * @namespace SimulateColorBlindValues
 * @see Visit {@link Actions.Effect|Effect} for an example
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
function deuteranopia() {
    return 'deuteranopia';
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
function protanopia() {
    return 'protanopia';
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
function tritanopia() {
    return 'tritanopia';
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
function tritanomaly() {
    return 'tritanomaly';
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
function deuteranomaly() {
    return 'deuteranomaly';
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
function coneMonochromacy() {
    return 'cone_monochromacy';
}
/**
 * @summary qualifier
 * @memberOf Qualifiers.SimulateColorBlindValues
 * @return {string}
 */
function rodMonochromacy() {
    return 'rod_monochromacy';
}
const SimulateColorBlind = {
    coneMonochromacy,
    deuteranomaly,
    deuteranopia,
    protanopia,
    rodMonochromacy,
    tritanomaly,
    tritanopia
};

exports.SimulateColorBlind = SimulateColorBlind;
exports.coneMonochromacy = coneMonochromacy;
exports.deuteranomaly = deuteranomaly;
exports.deuteranopia = deuteranopia;
exports.protanopia = protanopia;
exports.rodMonochromacy = rodMonochromacy;
exports.tritanomaly = tritanomaly;
exports.tritanopia = tritanopia;
