'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var backwards_utils_legacyNormalizeExpression = require('../utils/legacyNormalizeExpression.cjs');
require('../consts.cjs');

/**
 * Parse "if" parameter
 * Translates the condition if provided.
 * @private
 * @return {string} "if_" + ifValue
 */
function process_if(ifValue) {
    return ifValue ? "if_" + backwards_utils_legacyNormalizeExpression.legacyNormalizeExpression(ifValue) : ifValue;
}

exports.process_if = process_if;
