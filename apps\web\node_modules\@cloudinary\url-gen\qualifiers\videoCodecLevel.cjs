'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

/**
 * @description Contains functions to select the video codec level.
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/video_manipulation_and_delivery#video_codec_settings|Video codec settings}
 * @memberOf Qualifiers
 * @namespace VideoCodecLevel
 * @see Visit {@link Actions.Transcode|Transcode} for an example
 */
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
function vcl30() { return '3.0'; }
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
function vcl31() { return 3.1; }
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
function vcl40() { return '4.0'; }
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
function vcl41() { return 4.1; }
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
function vcl42() { return 4.2; }
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
function vcl50() { return '5.0'; }
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
function vcl51() { return 5.1; }
/**
 * @summary qualifier
 * @memberOf Qualifiers.VideoCodecLevel
 * @return {number}
 */
function vcl52() { return 5.2; }
const VideoCodecLevel = { vcl30, vcl31, vcl40, vcl41, vcl42, vcl50, vcl51, vcl52 };

exports.VideoCodecLevel = VideoCodecLevel;
exports.vcl30 = vcl30;
exports.vcl31 = vcl31;
exports.vcl40 = vcl40;
exports.vcl41 = vcl41;
exports.vcl42 = vcl42;
exports.vcl50 = vcl50;
exports.vcl51 = vcl51;
exports.vcl52 = vcl52;
