"use strict";
/*
 * Copyright Elasticsearch B.V. and contributors
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
const commonQueryParams = ['error_trace', 'filter_path', 'human', 'pretty'];
class Indices {
    constructor(transport) {
        Object.defineProperty(this, "transport", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "acceptedParams", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.transport = transport;
        this.acceptedParams = {
            'indices.add_block': {
                path: [
                    'index',
                    'block'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'master_timeout',
                    'timeout'
                ]
            },
            'indices.analyze': {
                path: [
                    'index'
                ],
                body: [
                    'analyzer',
                    'attributes',
                    'char_filter',
                    'explain',
                    'field',
                    'filter',
                    'normalizer',
                    'text',
                    'tokenizer'
                ],
                query: [
                    'index'
                ]
            },
            'indices.cancel_migrate_reindex': {
                path: [
                    'index'
                ],
                body: [],
                query: []
            },
            'indices.clear_cache': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'index',
                    'allow_no_indices',
                    'expand_wildcards',
                    'fielddata',
                    'fields',
                    'ignore_unavailable',
                    'query',
                    'request'
                ]
            },
            'indices.clone': {
                path: [
                    'index',
                    'target'
                ],
                body: [
                    'aliases',
                    'settings'
                ],
                query: [
                    'master_timeout',
                    'timeout',
                    'wait_for_active_shards'
                ]
            },
            'indices.close': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'master_timeout',
                    'timeout',
                    'wait_for_active_shards'
                ]
            },
            'indices.create': {
                path: [
                    'index'
                ],
                body: [
                    'aliases',
                    'mappings',
                    'settings'
                ],
                query: [
                    'master_timeout',
                    'timeout',
                    'wait_for_active_shards'
                ]
            },
            'indices.create_data_stream': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'master_timeout',
                    'timeout'
                ]
            },
            'indices.create_from': {
                path: [
                    'source',
                    'dest'
                ],
                body: [
                    'create_from'
                ],
                query: []
            },
            'indices.data_streams_stats': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'expand_wildcards'
                ]
            },
            'indices.delete': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'master_timeout',
                    'timeout'
                ]
            },
            'indices.delete_alias': {
                path: [
                    'index',
                    'name'
                ],
                body: [],
                query: [
                    'master_timeout',
                    'timeout'
                ]
            },
            'indices.delete_data_lifecycle': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'expand_wildcards',
                    'master_timeout',
                    'timeout'
                ]
            },
            'indices.delete_data_stream': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'master_timeout',
                    'expand_wildcards'
                ]
            },
            'indices.delete_index_template': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'master_timeout',
                    'timeout'
                ]
            },
            'indices.delete_template': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'master_timeout',
                    'timeout'
                ]
            },
            'indices.disk_usage': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'flush',
                    'ignore_unavailable',
                    'run_expensive_tasks'
                ]
            },
            'indices.downsample': {
                path: [
                    'index',
                    'target_index'
                ],
                body: [
                    'config'
                ],
                query: []
            },
            'indices.exists': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'flat_settings',
                    'ignore_unavailable',
                    'include_defaults',
                    'local'
                ]
            },
            'indices.exists_alias': {
                path: [
                    'name',
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'master_timeout'
                ]
            },
            'indices.exists_index_template': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'local',
                    'flat_settings',
                    'master_timeout'
                ]
            },
            'indices.exists_template': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'flat_settings',
                    'local',
                    'master_timeout'
                ]
            },
            'indices.explain_data_lifecycle': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'include_defaults',
                    'master_timeout'
                ]
            },
            'indices.field_usage_stats': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'fields'
                ]
            },
            'indices.flush': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'force',
                    'ignore_unavailable',
                    'wait_if_ongoing'
                ]
            },
            'indices.forcemerge': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'flush',
                    'ignore_unavailable',
                    'max_num_segments',
                    'only_expunge_deletes',
                    'wait_for_completion'
                ]
            },
            'indices.get': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'flat_settings',
                    'ignore_unavailable',
                    'include_defaults',
                    'local',
                    'master_timeout',
                    'features'
                ]
            },
            'indices.get_alias': {
                path: [
                    'name',
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'master_timeout'
                ]
            },
            'indices.get_data_lifecycle': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'expand_wildcards',
                    'include_defaults',
                    'master_timeout'
                ]
            },
            'indices.get_data_lifecycle_stats': {
                path: [],
                body: [],
                query: []
            },
            'indices.get_data_stream': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'expand_wildcards',
                    'include_defaults',
                    'master_timeout',
                    'verbose'
                ]
            },
            'indices.get_field_mapping': {
                path: [
                    'fields',
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'include_defaults',
                    'local'
                ]
            },
            'indices.get_index_template': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'local',
                    'flat_settings',
                    'master_timeout',
                    'include_defaults'
                ]
            },
            'indices.get_mapping': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'local',
                    'master_timeout'
                ]
            },
            'indices.get_migrate_reindex_status': {
                path: [
                    'index'
                ],
                body: [],
                query: []
            },
            'indices.get_settings': {
                path: [
                    'index',
                    'name'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'flat_settings',
                    'ignore_unavailable',
                    'include_defaults',
                    'local',
                    'master_timeout'
                ]
            },
            'indices.get_template': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'flat_settings',
                    'local',
                    'master_timeout'
                ]
            },
            'indices.migrate_reindex': {
                path: [],
                body: [
                    'reindex'
                ],
                query: []
            },
            'indices.migrate_to_data_stream': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'master_timeout',
                    'timeout'
                ]
            },
            'indices.modify_data_stream': {
                path: [],
                body: [
                    'actions'
                ],
                query: []
            },
            'indices.open': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'master_timeout',
                    'timeout',
                    'wait_for_active_shards'
                ]
            },
            'indices.promote_data_stream': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'master_timeout'
                ]
            },
            'indices.put_alias': {
                path: [
                    'index',
                    'name'
                ],
                body: [
                    'filter',
                    'index_routing',
                    'is_write_index',
                    'routing',
                    'search_routing'
                ],
                query: [
                    'master_timeout',
                    'timeout'
                ]
            },
            'indices.put_data_lifecycle': {
                path: [
                    'name'
                ],
                body: [
                    'data_retention',
                    'downsampling',
                    'enabled'
                ],
                query: [
                    'expand_wildcards',
                    'master_timeout',
                    'timeout'
                ]
            },
            'indices.put_index_template': {
                path: [
                    'name'
                ],
                body: [
                    'index_patterns',
                    'composed_of',
                    'template',
                    'data_stream',
                    'priority',
                    'version',
                    '_meta',
                    'allow_auto_create',
                    'ignore_missing_component_templates',
                    'deprecated'
                ],
                query: [
                    'create',
                    'master_timeout',
                    'cause'
                ]
            },
            'indices.put_mapping': {
                path: [
                    'index'
                ],
                body: [
                    'date_detection',
                    'dynamic',
                    'dynamic_date_formats',
                    'dynamic_templates',
                    '_field_names',
                    '_meta',
                    'numeric_detection',
                    'properties',
                    '_routing',
                    '_source',
                    'runtime'
                ],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'master_timeout',
                    'timeout',
                    'write_index_only'
                ]
            },
            'indices.put_settings': {
                path: [
                    'index'
                ],
                body: [
                    'settings'
                ],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'flat_settings',
                    'ignore_unavailable',
                    'master_timeout',
                    'preserve_existing',
                    'reopen',
                    'timeout'
                ]
            },
            'indices.put_template': {
                path: [
                    'name'
                ],
                body: [
                    'aliases',
                    'index_patterns',
                    'mappings',
                    'order',
                    'settings',
                    'version'
                ],
                query: [
                    'create',
                    'master_timeout',
                    'order',
                    'cause'
                ]
            },
            'indices.recovery': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'active_only',
                    'detailed'
                ]
            },
            'indices.refresh': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable'
                ]
            },
            'indices.reload_search_analyzers': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'resource'
                ]
            },
            'indices.resolve_cluster': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_throttled',
                    'ignore_unavailable',
                    'timeout'
                ]
            },
            'indices.resolve_index': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'expand_wildcards',
                    'ignore_unavailable',
                    'allow_no_indices'
                ]
            },
            'indices.rollover': {
                path: [
                    'alias',
                    'new_index'
                ],
                body: [
                    'aliases',
                    'conditions',
                    'mappings',
                    'settings'
                ],
                query: [
                    'dry_run',
                    'master_timeout',
                    'timeout',
                    'wait_for_active_shards',
                    'lazy'
                ]
            },
            'indices.segments': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable'
                ]
            },
            'indices.shard_stores': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'ignore_unavailable',
                    'status'
                ]
            },
            'indices.shrink': {
                path: [
                    'index',
                    'target'
                ],
                body: [
                    'aliases',
                    'settings'
                ],
                query: [
                    'master_timeout',
                    'timeout',
                    'wait_for_active_shards'
                ]
            },
            'indices.simulate_index_template': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'create',
                    'cause',
                    'master_timeout',
                    'include_defaults'
                ]
            },
            'indices.simulate_template': {
                path: [
                    'name'
                ],
                body: [
                    'allow_auto_create',
                    'index_patterns',
                    'composed_of',
                    'template',
                    'data_stream',
                    'priority',
                    'version',
                    '_meta',
                    'ignore_missing_component_templates',
                    'deprecated'
                ],
                query: [
                    'create',
                    'cause',
                    'master_timeout',
                    'include_defaults'
                ]
            },
            'indices.split': {
                path: [
                    'index',
                    'target'
                ],
                body: [
                    'aliases',
                    'settings'
                ],
                query: [
                    'master_timeout',
                    'timeout',
                    'wait_for_active_shards'
                ]
            },
            'indices.stats': {
                path: [
                    'metric',
                    'index'
                ],
                body: [],
                query: [
                    'completion_fields',
                    'expand_wildcards',
                    'fielddata_fields',
                    'fields',
                    'forbid_closed_indices',
                    'groups',
                    'include_segment_file_sizes',
                    'include_unloaded_segments',
                    'level'
                ]
            },
            'indices.update_aliases': {
                path: [],
                body: [
                    'actions'
                ],
                query: [
                    'master_timeout',
                    'timeout'
                ]
            },
            'indices.validate_query': {
                path: [
                    'index'
                ],
                body: [
                    'query'
                ],
                query: [
                    'allow_no_indices',
                    'all_shards',
                    'analyzer',
                    'analyze_wildcard',
                    'default_operator',
                    'df',
                    'expand_wildcards',
                    'explain',
                    'ignore_unavailable',
                    'lenient',
                    'rewrite',
                    'q'
                ]
            }
        };
    }
    async addBlock(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.add_block'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'PUT';
        const path = `/${encodeURIComponent(params.index.toString())}/_block/${encodeURIComponent(params.block.toString())}`;
        const meta = {
            name: 'indices.add_block',
            pathParts: {
                index: params.index,
                block: params.block
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async analyze(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.analyze'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = body != null ? 'POST' : 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_analyze`;
        }
        else {
            method = body != null ? 'POST' : 'GET';
            path = '/_analyze';
        }
        const meta = {
            name: 'indices.analyze',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async cancelMigrateReindex(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.cancel_migrate_reindex'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'POST';
        const path = `/_migration/reindex/${encodeURIComponent(params.index.toString())}/_cancel`;
        const meta = {
            name: 'indices.cancel_migrate_reindex',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async clearCache(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.clear_cache'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = 'POST';
            path = `/${encodeURIComponent(params.index.toString())}/_cache/clear`;
        }
        else {
            method = 'POST';
            path = '/_cache/clear';
        }
        const meta = {
            name: 'indices.clear_cache',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async clone(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.clone'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/${encodeURIComponent(params.index.toString())}/_clone/${encodeURIComponent(params.target.toString())}`;
        const meta = {
            name: 'indices.clone',
            pathParts: {
                index: params.index,
                target: params.target
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async close(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.close'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'POST';
        const path = `/${encodeURIComponent(params.index.toString())}/_close`;
        const meta = {
            name: 'indices.close',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async create(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.create'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/${encodeURIComponent(params.index.toString())}`;
        const meta = {
            name: 'indices.create',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async createDataStream(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.create_data_stream'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'PUT';
        const path = `/_data_stream/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.create_data_stream',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async createFrom(params, options) {
        var _a;
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.create_from'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body = (_a = params.body) !== null && _a !== void 0 ? _a : undefined;
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                // @ts-expect-error
                body = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_create_from/${encodeURIComponent(params.source.toString())}/${encodeURIComponent(params.dest.toString())}`;
        const meta = {
            name: 'indices.create_from',
            pathParts: {
                source: params.source,
                dest: params.dest
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async dataStreamsStats(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.data_streams_stats'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.name != null) {
            method = 'GET';
            path = `/_data_stream/${encodeURIComponent(params.name.toString())}/_stats`;
        }
        else {
            method = 'GET';
            path = '/_data_stream/_stats';
        }
        const meta = {
            name: 'indices.data_streams_stats',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async delete(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.delete'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'DELETE';
        const path = `/${encodeURIComponent(params.index.toString())}`;
        const meta = {
            name: 'indices.delete',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async deleteAlias(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.delete_alias'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null && params.name != null) {
            method = 'DELETE';
            path = `/${encodeURIComponent(params.index.toString())}/_alias/${encodeURIComponent(params.name.toString())}`;
        }
        else {
            method = 'DELETE';
            path = `/${encodeURIComponent(params.index.toString())}/_aliases/${encodeURIComponent(params.name.toString())}`;
        }
        const meta = {
            name: 'indices.delete_alias',
            pathParts: {
                index: params.index,
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async deleteDataLifecycle(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.delete_data_lifecycle'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'DELETE';
        const path = `/_data_stream/${encodeURIComponent(params.name.toString())}/_lifecycle`;
        const meta = {
            name: 'indices.delete_data_lifecycle',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async deleteDataStream(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.delete_data_stream'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'DELETE';
        const path = `/_data_stream/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.delete_data_stream',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async deleteIndexTemplate(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.delete_index_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'DELETE';
        const path = `/_index_template/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.delete_index_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async deleteTemplate(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.delete_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'DELETE';
        const path = `/_template/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.delete_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async diskUsage(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.disk_usage'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'POST';
        const path = `/${encodeURIComponent(params.index.toString())}/_disk_usage`;
        const meta = {
            name: 'indices.disk_usage',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async downsample(params, options) {
        var _a;
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.downsample'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body = (_a = params.body) !== null && _a !== void 0 ? _a : undefined;
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                // @ts-expect-error
                body = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = `/${encodeURIComponent(params.index.toString())}/_downsample/${encodeURIComponent(params.target_index.toString())}`;
        const meta = {
            name: 'indices.downsample',
            pathParts: {
                index: params.index,
                target_index: params.target_index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async exists(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.exists'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'HEAD';
        const path = `/${encodeURIComponent(params.index.toString())}`;
        const meta = {
            name: 'indices.exists',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async existsAlias(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.exists_alias'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null && params.name != null) {
            method = 'HEAD';
            path = `/${encodeURIComponent(params.index.toString())}/_alias/${encodeURIComponent(params.name.toString())}`;
        }
        else {
            method = 'HEAD';
            path = `/_alias/${encodeURIComponent(params.name.toString())}`;
        }
        const meta = {
            name: 'indices.exists_alias',
            pathParts: {
                name: params.name,
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async existsIndexTemplate(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.exists_index_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'HEAD';
        const path = `/_index_template/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.exists_index_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async existsTemplate(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.exists_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'HEAD';
        const path = `/_template/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.exists_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async explainDataLifecycle(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.explain_data_lifecycle'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = `/${encodeURIComponent(params.index.toString())}/_lifecycle/explain`;
        const meta = {
            name: 'indices.explain_data_lifecycle',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async fieldUsageStats(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.field_usage_stats'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = `/${encodeURIComponent(params.index.toString())}/_field_usage_stats`;
        const meta = {
            name: 'indices.field_usage_stats',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async flush(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.flush'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = body != null ? 'POST' : 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_flush`;
        }
        else {
            method = body != null ? 'POST' : 'GET';
            path = '/_flush';
        }
        const meta = {
            name: 'indices.flush',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async forcemerge(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.forcemerge'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = 'POST';
            path = `/${encodeURIComponent(params.index.toString())}/_forcemerge`;
        }
        else {
            method = 'POST';
            path = '/_forcemerge';
        }
        const meta = {
            name: 'indices.forcemerge',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async get(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.get'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = `/${encodeURIComponent(params.index.toString())}`;
        const meta = {
            name: 'indices.get',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getAlias(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.get_alias'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null && params.name != null) {
            method = 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_alias/${encodeURIComponent(params.name.toString())}`;
        }
        else if (params.name != null) {
            method = 'GET';
            path = `/_alias/${encodeURIComponent(params.name.toString())}`;
        }
        else if (params.index != null) {
            method = 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_alias`;
        }
        else {
            method = 'GET';
            path = '/_alias';
        }
        const meta = {
            name: 'indices.get_alias',
            pathParts: {
                name: params.name,
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getDataLifecycle(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.get_data_lifecycle'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = `/_data_stream/${encodeURIComponent(params.name.toString())}/_lifecycle`;
        const meta = {
            name: 'indices.get_data_lifecycle',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getDataLifecycleStats(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.get_data_lifecycle_stats'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = '/_lifecycle/stats';
        const meta = {
            name: 'indices.get_data_lifecycle_stats'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getDataStream(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.get_data_stream'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.name != null) {
            method = 'GET';
            path = `/_data_stream/${encodeURIComponent(params.name.toString())}`;
        }
        else {
            method = 'GET';
            path = '/_data_stream';
        }
        const meta = {
            name: 'indices.get_data_stream',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getFieldMapping(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.get_field_mapping'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null && params.fields != null) {
            method = 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_mapping/field/${encodeURIComponent(params.fields.toString())}`;
        }
        else {
            method = 'GET';
            path = `/_mapping/field/${encodeURIComponent(params.fields.toString())}`;
        }
        const meta = {
            name: 'indices.get_field_mapping',
            pathParts: {
                fields: params.fields,
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getIndexTemplate(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.get_index_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.name != null) {
            method = 'GET';
            path = `/_index_template/${encodeURIComponent(params.name.toString())}`;
        }
        else {
            method = 'GET';
            path = '/_index_template';
        }
        const meta = {
            name: 'indices.get_index_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getMapping(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.get_mapping'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_mapping`;
        }
        else {
            method = 'GET';
            path = '/_mapping';
        }
        const meta = {
            name: 'indices.get_mapping',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getMigrateReindexStatus(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.get_migrate_reindex_status'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = `/_migration/reindex/${encodeURIComponent(params.index.toString())}/_status`;
        const meta = {
            name: 'indices.get_migrate_reindex_status',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getSettings(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.get_settings'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null && params.name != null) {
            method = 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_settings/${encodeURIComponent(params.name.toString())}`;
        }
        else if (params.index != null) {
            method = 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_settings`;
        }
        else if (params.name != null) {
            method = 'GET';
            path = `/_settings/${encodeURIComponent(params.name.toString())}`;
        }
        else {
            method = 'GET';
            path = '/_settings';
        }
        const meta = {
            name: 'indices.get_settings',
            pathParts: {
                index: params.index,
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getTemplate(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.get_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.name != null) {
            method = 'GET';
            path = `/_template/${encodeURIComponent(params.name.toString())}`;
        }
        else {
            method = 'GET';
            path = '/_template';
        }
        const meta = {
            name: 'indices.get_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async migrateReindex(params, options) {
        var _a;
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.migrate_reindex'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body = (_a = params.body) !== null && _a !== void 0 ? _a : undefined;
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                // @ts-expect-error
                body = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = '/_migration/reindex';
        const meta = {
            name: 'indices.migrate_reindex'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async migrateToDataStream(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.migrate_to_data_stream'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'POST';
        const path = `/_data_stream/_migrate/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.migrate_to_data_stream',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async modifyDataStream(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.modify_data_stream'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = '/_data_stream/_modify';
        const meta = {
            name: 'indices.modify_data_stream'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async open(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.open'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'POST';
        const path = `/${encodeURIComponent(params.index.toString())}/_open`;
        const meta = {
            name: 'indices.open',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async promoteDataStream(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.promote_data_stream'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'POST';
        const path = `/_data_stream/_promote/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.promote_data_stream',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putAlias(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.put_alias'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        let method = '';
        let path = '';
        if (params.index != null && params.name != null) {
            method = 'PUT';
            path = `/${encodeURIComponent(params.index.toString())}/_alias/${encodeURIComponent(params.name.toString())}`;
        }
        else {
            method = 'PUT';
            path = `/${encodeURIComponent(params.index.toString())}/_aliases/${encodeURIComponent(params.name.toString())}`;
        }
        const meta = {
            name: 'indices.put_alias',
            pathParts: {
                index: params.index,
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putDataLifecycle(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.put_data_lifecycle'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_data_stream/${encodeURIComponent(params.name.toString())}/_lifecycle`;
        const meta = {
            name: 'indices.put_data_lifecycle',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putIndexTemplate(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.put_index_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_index_template/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.put_index_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putMapping(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.put_mapping'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/${encodeURIComponent(params.index.toString())}/_mapping`;
        const meta = {
            name: 'indices.put_mapping',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putSettings(params, options) {
        var _a;
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.put_settings'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body = (_a = params.body) !== null && _a !== void 0 ? _a : undefined;
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                // @ts-expect-error
                body = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = 'PUT';
            path = `/${encodeURIComponent(params.index.toString())}/_settings`;
        }
        else {
            method = 'PUT';
            path = '/_settings';
        }
        const meta = {
            name: 'indices.put_settings',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putTemplate(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.put_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_template/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.put_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async recovery(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.recovery'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_recovery`;
        }
        else {
            method = 'GET';
            path = '/_recovery';
        }
        const meta = {
            name: 'indices.recovery',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async refresh(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.refresh'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = body != null ? 'POST' : 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_refresh`;
        }
        else {
            method = body != null ? 'POST' : 'GET';
            path = '/_refresh';
        }
        const meta = {
            name: 'indices.refresh',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async reloadSearchAnalyzers(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.reload_search_analyzers'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = body != null ? 'POST' : 'GET';
        const path = `/${encodeURIComponent(params.index.toString())}/_reload_search_analyzers`;
        const meta = {
            name: 'indices.reload_search_analyzers',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async resolveCluster(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.resolve_cluster'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.name != null) {
            method = 'GET';
            path = `/_resolve/cluster/${encodeURIComponent(params.name.toString())}`;
        }
        else {
            method = 'GET';
            path = '/_resolve/cluster';
        }
        const meta = {
            name: 'indices.resolve_cluster',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async resolveIndex(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.resolve_index'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = `/_resolve/index/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.resolve_index',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async rollover(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.rollover'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        let method = '';
        let path = '';
        if (params.alias != null && params.new_index != null) {
            method = 'POST';
            path = `/${encodeURIComponent(params.alias.toString())}/_rollover/${encodeURIComponent(params.new_index.toString())}`;
        }
        else {
            method = 'POST';
            path = `/${encodeURIComponent(params.alias.toString())}/_rollover`;
        }
        const meta = {
            name: 'indices.rollover',
            pathParts: {
                alias: params.alias,
                new_index: params.new_index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async segments(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.segments'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_segments`;
        }
        else {
            method = 'GET';
            path = '/_segments';
        }
        const meta = {
            name: 'indices.segments',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async shardStores(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.shard_stores'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_shard_stores`;
        }
        else {
            method = 'GET';
            path = '/_shard_stores';
        }
        const meta = {
            name: 'indices.shard_stores',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async shrink(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.shrink'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/${encodeURIComponent(params.index.toString())}/_shrink/${encodeURIComponent(params.target.toString())}`;
        const meta = {
            name: 'indices.shrink',
            pathParts: {
                index: params.index,
                target: params.target
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async simulateIndexTemplate(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.simulate_index_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'POST';
        const path = `/_index_template/_simulate_index/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'indices.simulate_index_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async simulateTemplate(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.simulate_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        let method = '';
        let path = '';
        if (params.name != null) {
            method = 'POST';
            path = `/_index_template/_simulate/${encodeURIComponent(params.name.toString())}`;
        }
        else {
            method = 'POST';
            path = '/_index_template/_simulate';
        }
        const meta = {
            name: 'indices.simulate_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async split(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.split'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/${encodeURIComponent(params.index.toString())}/_split/${encodeURIComponent(params.target.toString())}`;
        const meta = {
            name: 'indices.split',
            pathParts: {
                index: params.index,
                target: params.target
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async stats(params, options) {
        const { path: acceptedPath } = this.acceptedParams['indices.stats'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null && params.metric != null) {
            method = 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_stats/${encodeURIComponent(params.metric.toString())}`;
        }
        else if (params.metric != null) {
            method = 'GET';
            path = `/_stats/${encodeURIComponent(params.metric.toString())}`;
        }
        else if (params.index != null) {
            method = 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_stats`;
        }
        else {
            method = 'GET';
            path = '/_stats';
        }
        const meta = {
            name: 'indices.stats',
            pathParts: {
                metric: params.metric,
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async updateAliases(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.update_aliases'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = '/_aliases';
        const meta = {
            name: 'indices.update_aliases'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async validateQuery(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['indices.validate_query'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = body != null ? 'POST' : 'GET';
            path = `/${encodeURIComponent(params.index.toString())}/_validate/query`;
        }
        else {
            method = body != null ? 'POST' : 'GET';
            path = '/_validate/query';
        }
        const meta = {
            name: 'indices.validate_query',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
}
exports.default = Indices;
//# sourceMappingURL=indices.js.map