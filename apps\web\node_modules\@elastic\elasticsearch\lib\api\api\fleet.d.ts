import { Transport, TransportRequestOptions, TransportRequestOptionsWithMeta, TransportRequestOptionsWithOutMeta, TransportResult } from '@elastic/transport';
import * as T from '../types';
interface That {
    transport: Transport;
    acceptedParams: Record<string, {
        path: string[];
        body: string[];
        query: string[];
    }>;
}
export default class Fleet {
    transport: Transport;
    acceptedParams: Record<string, {
        path: string[];
        body: string[];
        query: string[];
    }>;
    constructor(transport: Transport);
    /**
      * Deletes a secret stored by Fleet.
      */
    deleteSecret(this: That, params?: T.TODO, options?: TransportRequestOptionsWithOutMeta): Promise<T.TODO>;
    deleteSecret(this: That, params?: T.TODO, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.TODO, unknown>>;
    deleteSecret(this: That, params?: T.TODO, options?: TransportRequestOptions): Promise<T.TODO>;
    /**
      * Retrieves a secret stored by Fleet.
      */
    getSecret(this: That, params?: T.TODO, options?: TransportRequestOptionsWithOutMeta): Promise<T.TODO>;
    getSecret(this: That, params?: T.TODO, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.TODO, unknown>>;
    getSecret(this: That, params?: T.TODO, options?: TransportRequestOptions): Promise<T.TODO>;
    /**
      * Get global checkpoints. Get the current global checkpoints for an index. This API is designed for internal use by the Fleet server project.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/group/endpoint-fleet | Elasticsearch API documentation}
      */
    globalCheckpoints(this: That, params: T.FleetGlobalCheckpointsRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.FleetGlobalCheckpointsResponse>;
    globalCheckpoints(this: That, params: T.FleetGlobalCheckpointsRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.FleetGlobalCheckpointsResponse, unknown>>;
    globalCheckpoints(this: That, params: T.FleetGlobalCheckpointsRequest, options?: TransportRequestOptions): Promise<T.FleetGlobalCheckpointsResponse>;
    /**
      * Run multiple Fleet searches. Run several Fleet searches with a single API request. The API follows the same structure as the multi search API. However, similar to the Fleet search API, it supports the `wait_for_checkpoints` parameter.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/operation/operation-fleet-msearch | Elasticsearch API documentation}
      */
    msearch<TDocument = unknown>(this: That, params: T.FleetMsearchRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.FleetMsearchResponse<TDocument>>;
    msearch<TDocument = unknown>(this: That, params: T.FleetMsearchRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.FleetMsearchResponse<TDocument>, unknown>>;
    msearch<TDocument = unknown>(this: That, params: T.FleetMsearchRequest, options?: TransportRequestOptions): Promise<T.FleetMsearchResponse<TDocument>>;
    /**
      * Creates a secret stored by Fleet.
      */
    postSecret(this: That, params?: T.TODO, options?: TransportRequestOptionsWithOutMeta): Promise<T.TODO>;
    postSecret(this: That, params?: T.TODO, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.TODO, unknown>>;
    postSecret(this: That, params?: T.TODO, options?: TransportRequestOptions): Promise<T.TODO>;
    /**
      * Run a Fleet search. The purpose of the Fleet search API is to provide an API where the search will be run only after the provided checkpoint has been processed and is visible for searches inside of Elasticsearch.
      * @see {@link https://www.elastic.co/docs/api/doc/elasticsearch/v9/operation/operation-fleet-search | Elasticsearch API documentation}
      */
    search<TDocument = unknown>(this: That, params: T.FleetSearchRequest, options?: TransportRequestOptionsWithOutMeta): Promise<T.FleetSearchResponse<TDocument>>;
    search<TDocument = unknown>(this: That, params: T.FleetSearchRequest, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.FleetSearchResponse<TDocument>, unknown>>;
    search<TDocument = unknown>(this: That, params: T.FleetSearchRequest, options?: TransportRequestOptions): Promise<T.FleetSearchResponse<TDocument>>;
}
export {};
