import { Transport, TransportRequestOptions, TransportRequestOptionsWithMeta, TransportRequestOptionsWithOutMeta, TransportResult } from '@elastic/transport';
import * as T from '../types';
interface That {
    transport: Transport;
    acceptedParams: Record<string, {
        path: string[];
        body: string[];
        query: string[];
    }>;
}
export default class Profiling {
    transport: Transport;
    acceptedParams: Record<string, {
        path: string[];
        body: string[];
        query: string[];
    }>;
    constructor(transport: Transport);
    /**
      * Extracts a UI-optimized structure to render flamegraphs from Universal Profiling.
      * @see {@link https://www.elastic.co/guide/en/observability/9.0/universal-profiling.html | Elasticsearch API documentation}
      */
    flamegraph(this: That, params?: T.TODO, options?: TransportRequestOptionsWithOutMeta): Promise<T.TODO>;
    flamegraph(this: That, params?: T.TODO, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.TODO, unknown>>;
    flamegraph(this: That, params?: T.TODO, options?: TransportRequestOptions): Promise<T.TODO>;
    /**
      * Extracts raw stacktrace information from Universal Profiling.
      * @see {@link https://www.elastic.co/guide/en/observability/9.0/universal-profiling.html | Elasticsearch API documentation}
      */
    stacktraces(this: That, params?: T.TODO, options?: TransportRequestOptionsWithOutMeta): Promise<T.TODO>;
    stacktraces(this: That, params?: T.TODO, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.TODO, unknown>>;
    stacktraces(this: That, params?: T.TODO, options?: TransportRequestOptions): Promise<T.TODO>;
    /**
      * Returns basic information about the status of Universal Profiling.
      * @see {@link https://www.elastic.co/guide/en/observability/9.0/universal-profiling.html | Elasticsearch API documentation}
      */
    status(this: That, params?: T.TODO, options?: TransportRequestOptionsWithOutMeta): Promise<T.TODO>;
    status(this: That, params?: T.TODO, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.TODO, unknown>>;
    status(this: That, params?: T.TODO, options?: TransportRequestOptions): Promise<T.TODO>;
    /**
      * Extracts a list of topN functions from Universal Profiling.
      * @see {@link https://www.elastic.co/guide/en/observability/9.0/universal-profiling.html | Elasticsearch API documentation}
      */
    topnFunctions(this: That, params?: T.TODO, options?: TransportRequestOptionsWithOutMeta): Promise<T.TODO>;
    topnFunctions(this: That, params?: T.TODO, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<T.TODO, unknown>>;
    topnFunctions(this: That, params?: T.TODO, options?: TransportRequestOptions): Promise<T.TODO>;
}
export {};
