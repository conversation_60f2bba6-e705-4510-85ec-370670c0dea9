import Layer from './layer.js';
declare class FetchLayer extends Layer {
    /**
     * @class FetchLayer
     * @classdesc Creates an image layer using a remote URL.
     * @param {Object|string} options - layer parameters or a url
     * @param {string} options.url the url of the image to fetch
     */
    constructor(options: any);
    url(url: string): this;
    /**
     * generate the string representation of the layer
     * @function FetchLayer#toString
     * @return {String}
     */
    toString(): string;
}
export default FetchLayer;
