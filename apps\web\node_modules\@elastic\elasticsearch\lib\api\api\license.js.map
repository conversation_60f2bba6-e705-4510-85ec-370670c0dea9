{"version": 3, "file": "license.js", "sourceRoot": "", "sources": ["../../../../src/api/api/license.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AA0BH,MAAM,iBAAiB,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAA;AAE3E,MAAqB,OAAO;IAG1B,YAAa,SAAoB;QAFjC;;;;;WAAoB;QACpB;;;;;WAAmF;QAEjF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,cAAc,GAAG;YACpB,gBAAgB,EAAE;gBAChB,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE;oBACL,gBAAgB;oBAChB,SAAS;iBACV;aACF;YACD,aAAa,EAAE;gBACb,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE;oBACL,mBAAmB;oBACnB,OAAO;iBACR;aACF;YACD,0BAA0B,EAAE;gBAC1B,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE,EAAE;aACV;YACD,0BAA0B,EAAE;gBAC1B,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE,EAAE;aACV;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE;oBACJ,SAAS;oBACT,UAAU;iBACX;gBACD,KAAK,EAAE;oBACL,aAAa;oBACb,gBAAgB;oBAChB,SAAS;iBACV;aACF;YACD,0BAA0B,EAAE;gBAC1B,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE;oBACL,aAAa;oBACb,gBAAgB;oBAChB,SAAS;iBACV;aACF;YACD,0BAA0B,EAAE;gBAC1B,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE;oBACL,aAAa;oBACb,mBAAmB;oBACnB,gBAAgB;iBACjB;aACF;SACF,CAAA;IACH,CAAC;IASD,KAAK,CAAC,MAAM,CAAc,MAA+B,EAAE,OAAiC;QAC1F,MAAM,EACJ,IAAI,EAAE,YAAY,EACnB,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAA;QAEzC,MAAM,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA;QACrC,MAAM,WAAW,GAAwB,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAElF,IAAI,IAA8C,CAAA;QAClD,MAAM,QAAQ,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAA;QAC7B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,GAAG,QAAQ,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAA;YACxB,CAAC;QACH,CAAC;QAED,MAAM,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAA;QACrB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,SAAQ;YACV,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;gBACnD,mBAAmB;gBACnB,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,QAAQ,CAAA;QACvB,MAAM,IAAI,GAAG,WAAW,CAAA;QACxB,MAAM,IAAI,GAA6B;YACrC,IAAI,EAAE,gBAAgB;SACvB,CAAA;QACD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzF,CAAC;IASD,KAAK,CAAC,GAAG,CAAc,MAA4B,EAAE,OAAiC;QACpF,MAAM,EACJ,IAAI,EAAE,YAAY,EACnB,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAA;QAEtC,MAAM,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA;QACrC,MAAM,WAAW,GAAwB,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAElF,IAAI,IAA8C,CAAA;QAClD,MAAM,QAAQ,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAA;QAC7B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,GAAG,QAAQ,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAA;YACxB,CAAC;QACH,CAAC;QAED,MAAM,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAA;QACrB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,SAAQ;YACV,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;gBACnD,mBAAmB;gBACnB,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAA;QACpB,MAAM,IAAI,GAAG,WAAW,CAAA;QACxB,MAAM,IAAI,GAA6B;YACrC,IAAI,EAAE,aAAa;SACpB,CAAA;QACD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzF,CAAC;IASD,KAAK,CAAC,cAAc,CAAc,MAAuC,EAAE,OAAiC;QAC1G,MAAM,EACJ,IAAI,EAAE,YAAY,EACnB,GAAG,IAAI,CAAC,cAAc,CAAC,0BAA0B,CAAC,CAAA;QAEnD,MAAM,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA;QACrC,MAAM,WAAW,GAAwB,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAElF,IAAI,IAA8C,CAAA;QAClD,MAAM,QAAQ,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAA;QAC7B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,GAAG,QAAQ,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAA;YACxB,CAAC;QACH,CAAC;QAED,MAAM,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAA;QACrB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,SAAQ;YACV,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;gBACnD,mBAAmB;gBACnB,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAA;QACpB,MAAM,IAAI,GAAG,wBAAwB,CAAA;QACrC,MAAM,IAAI,GAA6B;YACrC,IAAI,EAAE,0BAA0B;SACjC,CAAA;QACD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzF,CAAC;IASD,KAAK,CAAC,cAAc,CAAc,MAAuC,EAAE,OAAiC;QAC1G,MAAM,EACJ,IAAI,EAAE,YAAY,EACnB,GAAG,IAAI,CAAC,cAAc,CAAC,0BAA0B,CAAC,CAAA;QAEnD,MAAM,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA;QACrC,MAAM,WAAW,GAAwB,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAElF,IAAI,IAA8C,CAAA;QAClD,MAAM,QAAQ,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAA;QAC7B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,GAAG,QAAQ,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAA;YACxB,CAAC;QACH,CAAC;QAED,MAAM,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAA;QACrB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,SAAQ;YACV,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;gBACnD,mBAAmB;gBACnB,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAA;QACpB,MAAM,IAAI,GAAG,wBAAwB,CAAA;QACrC,MAAM,IAAI,GAA6B;YACrC,IAAI,EAAE,0BAA0B;SACjC,CAAA;QACD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzF,CAAC;IASD,KAAK,CAAC,IAAI,CAAc,MAA6B,EAAE,OAAiC;QACtF,MAAM,EACJ,IAAI,EAAE,YAAY,EAClB,IAAI,EAAE,YAAY,EAClB,KAAK,EAAE,aAAa,EACrB,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;QAEvC,MAAM,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA;QACrC,MAAM,WAAW,GAAwB,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAElF,IAAI,IAA8C,CAAA;QAClD,MAAM,QAAQ,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAA;QAC7B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,GAAG,QAAQ,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAA;YACxB,CAAC;QACH,CAAC;QAED,MAAM,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAA;QACrB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,IAAI,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,EAAE,CAAA;gBACjB,mBAAmB;gBACnB,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YACzB,CAAC;iBAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtC,SAAQ;YACV,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;gBACnD,IAAI,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACnE,mBAAmB;oBACnB,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;gBAChC,CAAC;qBAAM,CAAC;oBACN,IAAI,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,EAAE,CAAA;oBACjB,mBAAmB;oBACnB,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;gBACzB,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAA;QACpB,MAAM,IAAI,GAAG,WAAW,CAAA;QACxB,MAAM,IAAI,GAA6B;YACrC,IAAI,EAAE,cAAc;SACrB,CAAA;QACD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzF,CAAC;IASD,KAAK,CAAC,cAAc,CAAc,MAAuC,EAAE,OAAiC;QAC1G,MAAM,EACJ,IAAI,EAAE,YAAY,EACnB,GAAG,IAAI,CAAC,cAAc,CAAC,0BAA0B,CAAC,CAAA;QAEnD,MAAM,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA;QACrC,MAAM,WAAW,GAAwB,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAElF,IAAI,IAA8C,CAAA;QAClD,MAAM,QAAQ,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAA;QAC7B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,GAAG,QAAQ,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAA;YACxB,CAAC;QACH,CAAC;QAED,MAAM,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAA;QACrB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,SAAQ;YACV,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;gBACnD,mBAAmB;gBACnB,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAA;QACrB,MAAM,IAAI,GAAG,uBAAuB,CAAA;QACpC,MAAM,IAAI,GAA6B;YACrC,IAAI,EAAE,0BAA0B;SACjC,CAAA;QACD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzF,CAAC;IASD,KAAK,CAAC,cAAc,CAAc,MAAuC,EAAE,OAAiC;QAC1G,MAAM,EACJ,IAAI,EAAE,YAAY,EACnB,GAAG,IAAI,CAAC,cAAc,CAAC,0BAA0B,CAAC,CAAA;QAEnD,MAAM,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA;QACrC,MAAM,WAAW,GAAwB,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAElF,IAAI,IAA8C,CAAA;QAClD,MAAM,QAAQ,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAA;QAC7B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,GAAG,QAAQ,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAA;YACxB,CAAC;QACH,CAAC;QAED,MAAM,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAA;QACrB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,SAAQ;YACV,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;gBACnD,mBAAmB;gBACnB,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAA;QACrB,MAAM,IAAI,GAAG,uBAAuB,CAAA;QACpC,MAAM,IAAI,GAA6B;YACrC,IAAI,EAAE,0BAA0B;SACjC,CAAA;QACD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzF,CAAC;CACF;AAzXD,0BAyXC"}