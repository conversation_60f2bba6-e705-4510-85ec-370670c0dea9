"use strict";
/*
 * Copyright Elasticsearch B.V. and contributors
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
const commonQueryParams = ['error_trace', 'filter_path', 'human', 'pretty'];
class Inference {
    constructor(transport) {
        Object.defineProperty(this, "transport", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "acceptedParams", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.transport = transport;
        this.acceptedParams = {
            'inference.chat_completion_unified': {
                path: [
                    'inference_id'
                ],
                body: [
                    'chat_completion_request'
                ],
                query: [
                    'timeout'
                ]
            },
            'inference.completion': {
                path: [
                    'inference_id'
                ],
                body: [
                    'input',
                    'task_settings'
                ],
                query: [
                    'timeout'
                ]
            },
            'inference.delete': {
                path: [
                    'task_type',
                    'inference_id'
                ],
                body: [],
                query: [
                    'dry_run',
                    'force'
                ]
            },
            'inference.get': {
                path: [
                    'task_type',
                    'inference_id'
                ],
                body: [],
                query: []
            },
            'inference.inference': {
                path: [
                    'task_type',
                    'inference_id'
                ],
                body: [
                    'query',
                    'input',
                    'task_settings'
                ],
                query: [
                    'timeout'
                ]
            },
            'inference.put': {
                path: [
                    'task_type',
                    'inference_id'
                ],
                body: [
                    'inference_config'
                ],
                query: []
            },
            'inference.put_alibabacloud': {
                path: [
                    'task_type',
                    'alibabacloud_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings',
                    'task_settings'
                ],
                query: []
            },
            'inference.put_amazonbedrock': {
                path: [
                    'task_type',
                    'amazonbedrock_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings',
                    'task_settings'
                ],
                query: []
            },
            'inference.put_anthropic': {
                path: [
                    'task_type',
                    'anthropic_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings',
                    'task_settings'
                ],
                query: []
            },
            'inference.put_azureaistudio': {
                path: [
                    'task_type',
                    'azureaistudio_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings',
                    'task_settings'
                ],
                query: []
            },
            'inference.put_azureopenai': {
                path: [
                    'task_type',
                    'azureopenai_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings',
                    'task_settings'
                ],
                query: []
            },
            'inference.put_cohere': {
                path: [
                    'task_type',
                    'cohere_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings',
                    'task_settings'
                ],
                query: []
            },
            'inference.put_elasticsearch': {
                path: [
                    'task_type',
                    'elasticsearch_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings',
                    'task_settings'
                ],
                query: []
            },
            'inference.put_elser': {
                path: [
                    'task_type',
                    'elser_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings'
                ],
                query: []
            },
            'inference.put_googleaistudio': {
                path: [
                    'task_type',
                    'googleaistudio_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings'
                ],
                query: []
            },
            'inference.put_googlevertexai': {
                path: [
                    'task_type',
                    'googlevertexai_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings',
                    'task_settings'
                ],
                query: []
            },
            'inference.put_hugging_face': {
                path: [
                    'task_type',
                    'huggingface_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings'
                ],
                query: []
            },
            'inference.put_jinaai': {
                path: [
                    'task_type',
                    'jinaai_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings',
                    'task_settings'
                ],
                query: []
            },
            'inference.put_mistral': {
                path: [
                    'task_type',
                    'mistral_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings'
                ],
                query: []
            },
            'inference.put_openai': {
                path: [
                    'task_type',
                    'openai_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings',
                    'task_settings'
                ],
                query: []
            },
            'inference.put_voyageai': {
                path: [
                    'task_type',
                    'voyageai_inference_id'
                ],
                body: [
                    'chunking_settings',
                    'service',
                    'service_settings',
                    'task_settings'
                ],
                query: []
            },
            'inference.put_watsonx': {
                path: [
                    'task_type',
                    'watsonx_inference_id'
                ],
                body: [
                    'service',
                    'service_settings'
                ],
                query: []
            },
            'inference.rerank': {
                path: [
                    'inference_id'
                ],
                body: [
                    'query',
                    'input',
                    'task_settings'
                ],
                query: [
                    'timeout'
                ]
            },
            'inference.sparse_embedding': {
                path: [
                    'inference_id'
                ],
                body: [
                    'input',
                    'task_settings'
                ],
                query: [
                    'timeout'
                ]
            },
            'inference.stream_completion': {
                path: [
                    'inference_id'
                ],
                body: [
                    'input',
                    'task_settings'
                ],
                query: []
            },
            'inference.text_embedding': {
                path: [
                    'inference_id'
                ],
                body: [
                    'input',
                    'task_settings'
                ],
                query: [
                    'timeout'
                ]
            },
            'inference.update': {
                path: [
                    'inference_id',
                    'task_type'
                ],
                body: [
                    'inference_config'
                ],
                query: []
            }
        };
    }
    async chatCompletionUnified(params, options) {
        var _a;
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.chat_completion_unified'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body = (_a = params.body) !== null && _a !== void 0 ? _a : undefined;
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                // @ts-expect-error
                body = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = `/_inference/chat_completion/${encodeURIComponent(params.inference_id.toString())}/_stream`;
        const meta = {
            name: 'inference.chat_completion_unified',
            pathParts: {
                inference_id: params.inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async completion(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.completion'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = `/_inference/completion/${encodeURIComponent(params.inference_id.toString())}`;
        const meta = {
            name: 'inference.completion',
            pathParts: {
                inference_id: params.inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async delete(params, options) {
        const { path: acceptedPath } = this.acceptedParams['inference.delete'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.task_type != null && params.inference_id != null) {
            method = 'DELETE';
            path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.inference_id.toString())}`;
        }
        else {
            method = 'DELETE';
            path = `/_inference/${encodeURIComponent(params.inference_id.toString())}`;
        }
        const meta = {
            name: 'inference.delete',
            pathParts: {
                task_type: params.task_type,
                inference_id: params.inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async get(params, options) {
        const { path: acceptedPath } = this.acceptedParams['inference.get'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.task_type != null && params.inference_id != null) {
            method = 'GET';
            path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.inference_id.toString())}`;
        }
        else if (params.inference_id != null) {
            method = 'GET';
            path = `/_inference/${encodeURIComponent(params.inference_id.toString())}`;
        }
        else {
            method = 'GET';
            path = '/_inference';
        }
        const meta = {
            name: 'inference.get',
            pathParts: {
                task_type: params.task_type,
                inference_id: params.inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async inference(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.inference'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        let method = '';
        let path = '';
        if (params.task_type != null && params.inference_id != null) {
            method = 'POST';
            path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.inference_id.toString())}`;
        }
        else {
            method = 'POST';
            path = `/_inference/${encodeURIComponent(params.inference_id.toString())}`;
        }
        const meta = {
            name: 'inference.inference',
            pathParts: {
                task_type: params.task_type,
                inference_id: params.inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async put(params, options) {
        var _a;
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body = (_a = params.body) !== null && _a !== void 0 ? _a : undefined;
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                // @ts-expect-error
                body = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        let method = '';
        let path = '';
        if (params.task_type != null && params.inference_id != null) {
            method = 'PUT';
            path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.inference_id.toString())}`;
        }
        else {
            method = 'PUT';
            path = `/_inference/${encodeURIComponent(params.inference_id.toString())}`;
        }
        const meta = {
            name: 'inference.put',
            pathParts: {
                task_type: params.task_type,
                inference_id: params.inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putAlibabacloud(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_alibabacloud'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.alibabacloud_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_alibabacloud',
            pathParts: {
                task_type: params.task_type,
                alibabacloud_inference_id: params.alibabacloud_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putAmazonbedrock(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_amazonbedrock'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.amazonbedrock_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_amazonbedrock',
            pathParts: {
                task_type: params.task_type,
                amazonbedrock_inference_id: params.amazonbedrock_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putAnthropic(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_anthropic'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.anthropic_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_anthropic',
            pathParts: {
                task_type: params.task_type,
                anthropic_inference_id: params.anthropic_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putAzureaistudio(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_azureaistudio'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.azureaistudio_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_azureaistudio',
            pathParts: {
                task_type: params.task_type,
                azureaistudio_inference_id: params.azureaistudio_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putAzureopenai(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_azureopenai'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.azureopenai_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_azureopenai',
            pathParts: {
                task_type: params.task_type,
                azureopenai_inference_id: params.azureopenai_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putCohere(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_cohere'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.cohere_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_cohere',
            pathParts: {
                task_type: params.task_type,
                cohere_inference_id: params.cohere_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putElasticsearch(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_elasticsearch'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.elasticsearch_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_elasticsearch',
            pathParts: {
                task_type: params.task_type,
                elasticsearch_inference_id: params.elasticsearch_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putElser(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_elser'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.elser_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_elser',
            pathParts: {
                task_type: params.task_type,
                elser_inference_id: params.elser_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putGoogleaistudio(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_googleaistudio'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.googleaistudio_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_googleaistudio',
            pathParts: {
                task_type: params.task_type,
                googleaistudio_inference_id: params.googleaistudio_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putGooglevertexai(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_googlevertexai'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.googlevertexai_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_googlevertexai',
            pathParts: {
                task_type: params.task_type,
                googlevertexai_inference_id: params.googlevertexai_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putHuggingFace(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_hugging_face'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.huggingface_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_hugging_face',
            pathParts: {
                task_type: params.task_type,
                huggingface_inference_id: params.huggingface_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putJinaai(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_jinaai'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.jinaai_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_jinaai',
            pathParts: {
                task_type: params.task_type,
                jinaai_inference_id: params.jinaai_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putMistral(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_mistral'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.mistral_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_mistral',
            pathParts: {
                task_type: params.task_type,
                mistral_inference_id: params.mistral_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putOpenai(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_openai'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.openai_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_openai',
            pathParts: {
                task_type: params.task_type,
                openai_inference_id: params.openai_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putVoyageai(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_voyageai'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.voyageai_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_voyageai',
            pathParts: {
                task_type: params.task_type,
                voyageai_inference_id: params.voyageai_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putWatsonx(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.put_watsonx'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.watsonx_inference_id.toString())}`;
        const meta = {
            name: 'inference.put_watsonx',
            pathParts: {
                task_type: params.task_type,
                watsonx_inference_id: params.watsonx_inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async rerank(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.rerank'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = `/_inference/rerank/${encodeURIComponent(params.inference_id.toString())}`;
        const meta = {
            name: 'inference.rerank',
            pathParts: {
                inference_id: params.inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async sparseEmbedding(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.sparse_embedding'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = `/_inference/sparse_embedding/${encodeURIComponent(params.inference_id.toString())}`;
        const meta = {
            name: 'inference.sparse_embedding',
            pathParts: {
                inference_id: params.inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async streamCompletion(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.stream_completion'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = `/_inference/completion/${encodeURIComponent(params.inference_id.toString())}/_stream`;
        const meta = {
            name: 'inference.stream_completion',
            pathParts: {
                inference_id: params.inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async textEmbedding(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.text_embedding'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = `/_inference/text_embedding/${encodeURIComponent(params.inference_id.toString())}`;
        const meta = {
            name: 'inference.text_embedding',
            pathParts: {
                inference_id: params.inference_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async update(params, options) {
        var _a;
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['inference.update'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body = (_a = params.body) !== null && _a !== void 0 ? _a : undefined;
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                // @ts-expect-error
                body = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        let method = '';
        let path = '';
        if (params.task_type != null && params.inference_id != null) {
            method = 'PUT';
            path = `/_inference/${encodeURIComponent(params.task_type.toString())}/${encodeURIComponent(params.inference_id.toString())}/_update`;
        }
        else {
            method = 'PUT';
            path = `/_inference/${encodeURIComponent(params.inference_id.toString())}/_update`;
        }
        const meta = {
            name: 'inference.update',
            pathParts: {
                inference_id: params.inference_id,
                task_type: params.task_type
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
}
exports.default = Inference;
//# sourceMappingURL=inference.js.map