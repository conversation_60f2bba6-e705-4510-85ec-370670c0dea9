{"version": 3, "file": "autoscaling.js", "sourceRoot": "", "sources": ["../../../../src/api/api/autoscaling.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AA0BH,MAAM,iBAAiB,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAA;AAE3E,MAAqB,WAAW;IAG9B,YAAa,SAAoB;QAFjC;;;;;WAAoB;QACpB;;;;;WAAmF;QAEjF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,cAAc,GAAG;YACpB,uCAAuC,EAAE;gBACvC,IAAI,EAAE;oBACJ,MAAM;iBACP;gBACD,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE;oBACL,gBAAgB;oBAChB,SAAS;iBACV;aACF;YACD,sCAAsC,EAAE;gBACtC,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE;oBACL,gBAAgB;iBACjB;aACF;YACD,oCAAoC,EAAE;gBACpC,IAAI,EAAE;oBACJ,MAAM;iBACP;gBACD,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE;oBACL,gBAAgB;iBACjB;aACF;YACD,oCAAoC,EAAE;gBACpC,IAAI,EAAE;oBACJ,MAAM;iBACP;gBACD,IAAI,EAAE;oBACJ,QAAQ;iBACT;gBACD,KAAK,EAAE;oBACL,gBAAgB;oBAChB,SAAS;iBACV;aACF;SACF,CAAA;IACH,CAAC;IASD,KAAK,CAAC,uBAAuB,CAAc,MAAmD,EAAE,OAAiC;QAC/H,MAAM,EACJ,IAAI,EAAE,YAAY,EACnB,GAAG,IAAI,CAAC,cAAc,CAAC,uCAAuC,CAAC,CAAA;QAEhE,MAAM,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA;QACrC,MAAM,WAAW,GAAwB,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAElF,IAAI,IAA8C,CAAA;QAClD,MAAM,QAAQ,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAA;QAC7B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,GAAG,QAAQ,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAA;YACxB,CAAC;QACH,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,SAAQ;YACV,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;gBACnD,mBAAmB;gBACnB,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,QAAQ,CAAA;QACvB,MAAM,IAAI,GAAG,wBAAwB,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAA;QACjF,MAAM,IAAI,GAA6B;YACrC,IAAI,EAAE,uCAAuC;YAC7C,SAAS,EAAE;gBACT,IAAI,EAAE,MAAM,CAAC,IAAI;aAClB;SACF,CAAA;QACD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzF,CAAC;IASD,KAAK,CAAC,sBAAsB,CAAc,MAAmD,EAAE,OAAiC;QAC9H,MAAM,EACJ,IAAI,EAAE,YAAY,EACnB,GAAG,IAAI,CAAC,cAAc,CAAC,sCAAsC,CAAC,CAAA;QAE/D,MAAM,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA;QACrC,MAAM,WAAW,GAAwB,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAElF,IAAI,IAA8C,CAAA;QAClD,MAAM,QAAQ,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAA;QAC7B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,GAAG,QAAQ,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAA;YACxB,CAAC;QACH,CAAC;QAED,MAAM,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAA;QACrB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,SAAQ;YACV,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;gBACnD,mBAAmB;gBACnB,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAA;QACpB,MAAM,IAAI,GAAG,wBAAwB,CAAA;QACrC,MAAM,IAAI,GAA6B;YACrC,IAAI,EAAE,sCAAsC;SAC7C,CAAA;QACD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzF,CAAC;IASD,KAAK,CAAC,oBAAoB,CAAc,MAAgD,EAAE,OAAiC;QACzH,MAAM,EACJ,IAAI,EAAE,YAAY,EACnB,GAAG,IAAI,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAA;QAE7D,MAAM,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA;QACrC,MAAM,WAAW,GAAwB,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAElF,IAAI,IAA8C,CAAA;QAClD,MAAM,QAAQ,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAA;QAC7B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,GAAG,QAAQ,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAA;YACxB,CAAC;QACH,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,SAAQ;YACV,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;gBACnD,mBAAmB;gBACnB,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAA;QACpB,MAAM,IAAI,GAAG,wBAAwB,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAA;QACjF,MAAM,IAAI,GAA6B;YACrC,IAAI,EAAE,oCAAoC;YAC1C,SAAS,EAAE;gBACT,IAAI,EAAE,MAAM,CAAC,IAAI;aAClB;SACF,CAAA;QACD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzF,CAAC;IASD,KAAK,CAAC,oBAAoB,CAAc,MAAgD,EAAE,OAAiC;;QACzH,MAAM,EACJ,IAAI,EAAE,YAAY,EAClB,IAAI,EAAE,YAAY,EAClB,KAAK,EAAE,aAAa,EACrB,GAAG,IAAI,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAA;QAE7D,MAAM,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA;QACrC,MAAM,WAAW,GAAwB,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAElF,IAAI,IAAI,GAAQ,MAAA,MAAM,CAAC,IAAI,mCAAI,SAAS,CAAA;QACxC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,mBAAmB;gBACnB,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YACpB,CAAC;iBAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtC,SAAQ;YACV,CAAC;iBAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,aAAa,EAAE,CAAC;gBACnD,IAAI,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACnE,mBAAmB;oBACnB,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;gBAChC,CAAC;qBAAM,CAAC;oBACN,IAAI,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,EAAE,CAAA;oBACjB,mBAAmB;oBACnB,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;gBACzB,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAA;QACpB,MAAM,IAAI,GAAG,wBAAwB,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAA;QACjF,MAAM,IAAI,GAA6B;YACrC,IAAI,EAAE,oCAAoC;YAC1C,SAAS,EAAE;gBACT,IAAI,EAAE,MAAM,CAAC,IAAI;aAClB;SACF,CAAA;QACD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzF,CAAC;CACF;AAlOD,8BAkOC"}