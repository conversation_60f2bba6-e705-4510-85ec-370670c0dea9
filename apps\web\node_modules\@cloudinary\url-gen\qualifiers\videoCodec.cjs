'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var videoCodec = require('../videoCodec-b8fe49c0.cjs');
require('../VideoCodecType-69d56a1b.cjs');
require('../Qualifier-6633a22f.cjs');
require('../QualifierValue-e770d619.cjs');
require('../QualifierModel-0923d819.cjs');
require('../unsupportedError-74070138.cjs');



exports.VideoCodec = videoCodec.VideoCodec;
exports.auto = videoCodec.auto;
exports.h264 = videoCodec.h264;
exports.h265 = videoCodec.h265;
exports.proRes = videoCodec.proRes;
exports.theora = videoCodec.theora;
exports.vp8 = videoCodec.vp8;
exports.vp9 = videoCodec.vp9;
