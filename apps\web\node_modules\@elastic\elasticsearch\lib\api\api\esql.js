"use strict";
/*
 * Copyright Elasticsearch B.V. and contributors
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
const commonQueryParams = ['error_trace', 'filter_path', 'human', 'pretty'];
class Esql {
    constructor(transport) {
        Object.defineProperty(this, "transport", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "acceptedParams", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.transport = transport;
        this.acceptedParams = {
            'esql.async_query': {
                path: [],
                body: [
                    'columnar',
                    'filter',
                    'locale',
                    'params',
                    'profile',
                    'query',
                    'tables',
                    'include_ccs_metadata',
                    'wait_for_completion_timeout'
                ],
                query: [
                    'delimiter',
                    'drop_null_columns',
                    'format',
                    'keep_alive',
                    'keep_on_completion',
                    'wait_for_completion_timeout'
                ]
            },
            'esql.async_query_delete': {
                path: [
                    'id'
                ],
                body: [],
                query: []
            },
            'esql.async_query_get': {
                path: [
                    'id'
                ],
                body: [],
                query: [
                    'drop_null_columns',
                    'keep_alive',
                    'wait_for_completion_timeout'
                ]
            },
            'esql.async_query_stop': {
                path: [
                    'id'
                ],
                body: [],
                query: [
                    'drop_null_columns'
                ]
            },
            'esql.query': {
                path: [],
                body: [
                    'columnar',
                    'filter',
                    'locale',
                    'params',
                    'profile',
                    'query',
                    'tables',
                    'include_ccs_metadata'
                ],
                query: [
                    'format',
                    'delimiter',
                    'drop_null_columns'
                ]
            }
        };
    }
    async asyncQuery(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['esql.async_query'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = '/_query/async';
        const meta = {
            name: 'esql.async_query'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async asyncQueryDelete(params, options) {
        const { path: acceptedPath } = this.acceptedParams['esql.async_query_delete'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'DELETE';
        const path = `/_query/async/${encodeURIComponent(params.id.toString())}`;
        const meta = {
            name: 'esql.async_query_delete',
            pathParts: {
                id: params.id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async asyncQueryGet(params, options) {
        const { path: acceptedPath } = this.acceptedParams['esql.async_query_get'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = `/_query/async/${encodeURIComponent(params.id.toString())}`;
        const meta = {
            name: 'esql.async_query_get',
            pathParts: {
                id: params.id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async asyncQueryStop(params, options) {
        const { path: acceptedPath } = this.acceptedParams['esql.async_query_stop'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'POST';
        const path = `/_query/async/${encodeURIComponent(params.id.toString())}/stop`;
        const meta = {
            name: 'esql.async_query_stop',
            pathParts: {
                id: params.id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async query(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['esql.query'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = '/_query';
        const meta = {
            name: 'esql.query'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
}
exports.default = Esql;
//# sourceMappingURL=esql.js.map