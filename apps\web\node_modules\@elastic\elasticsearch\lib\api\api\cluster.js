"use strict";
/*
 * Copyright Elasticsearch B.V. and contributors
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
const commonQueryParams = ['error_trace', 'filter_path', 'human', 'pretty'];
class Cluster {
    constructor(transport) {
        Object.defineProperty(this, "transport", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "acceptedParams", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.transport = transport;
        this.acceptedParams = {
            'cluster.allocation_explain': {
                path: [],
                body: [
                    'current_node',
                    'index',
                    'primary',
                    'shard'
                ],
                query: [
                    'include_disk_info',
                    'include_yes_decisions',
                    'master_timeout'
                ]
            },
            'cluster.delete_component_template': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'master_timeout',
                    'timeout'
                ]
            },
            'cluster.delete_voting_config_exclusions': {
                path: [],
                body: [],
                query: [
                    'master_timeout',
                    'wait_for_removal'
                ]
            },
            'cluster.exists_component_template': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'master_timeout',
                    'local'
                ]
            },
            'cluster.get_component_template': {
                path: [
                    'name'
                ],
                body: [],
                query: [
                    'flat_settings',
                    'include_defaults',
                    'local',
                    'master_timeout'
                ]
            },
            'cluster.get_settings': {
                path: [],
                body: [],
                query: [
                    'flat_settings',
                    'include_defaults',
                    'master_timeout',
                    'timeout'
                ]
            },
            'cluster.health': {
                path: [
                    'index'
                ],
                body: [],
                query: [
                    'expand_wildcards',
                    'level',
                    'local',
                    'master_timeout',
                    'timeout',
                    'wait_for_active_shards',
                    'wait_for_events',
                    'wait_for_nodes',
                    'wait_for_no_initializing_shards',
                    'wait_for_no_relocating_shards',
                    'wait_for_status'
                ]
            },
            'cluster.info': {
                path: [
                    'target'
                ],
                body: [],
                query: []
            },
            'cluster.pending_tasks': {
                path: [],
                body: [],
                query: [
                    'local',
                    'master_timeout'
                ]
            },
            'cluster.post_voting_config_exclusions': {
                path: [],
                body: [],
                query: [
                    'node_names',
                    'node_ids',
                    'master_timeout',
                    'timeout'
                ]
            },
            'cluster.put_component_template': {
                path: [
                    'name'
                ],
                body: [
                    'template',
                    'version',
                    '_meta',
                    'deprecated'
                ],
                query: [
                    'create',
                    'master_timeout'
                ]
            },
            'cluster.put_settings': {
                path: [],
                body: [
                    'persistent',
                    'transient'
                ],
                query: [
                    'flat_settings',
                    'master_timeout',
                    'timeout'
                ]
            },
            'cluster.remote_info': {
                path: [],
                body: [],
                query: []
            },
            'cluster.reroute': {
                path: [],
                body: [
                    'commands'
                ],
                query: [
                    'dry_run',
                    'explain',
                    'metric',
                    'retry_failed',
                    'master_timeout',
                    'timeout'
                ]
            },
            'cluster.state': {
                path: [
                    'metric',
                    'index'
                ],
                body: [],
                query: [
                    'allow_no_indices',
                    'expand_wildcards',
                    'flat_settings',
                    'ignore_unavailable',
                    'local',
                    'master_timeout',
                    'wait_for_metadata_version',
                    'wait_for_timeout'
                ]
            },
            'cluster.stats': {
                path: [
                    'node_id'
                ],
                body: [],
                query: [
                    'include_remotes',
                    'timeout'
                ]
            }
        };
    }
    async allocationExplain(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['cluster.allocation_explain'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = body != null ? 'POST' : 'GET';
        const path = '/_cluster/allocation/explain';
        const meta = {
            name: 'cluster.allocation_explain'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async deleteComponentTemplate(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.delete_component_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'DELETE';
        const path = `/_component_template/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'cluster.delete_component_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async deleteVotingConfigExclusions(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.delete_voting_config_exclusions'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'DELETE';
        const path = '/_cluster/voting_config_exclusions';
        const meta = {
            name: 'cluster.delete_voting_config_exclusions'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async existsComponentTemplate(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.exists_component_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'HEAD';
        const path = `/_component_template/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'cluster.exists_component_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getComponentTemplate(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.get_component_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.name != null) {
            method = 'GET';
            path = `/_component_template/${encodeURIComponent(params.name.toString())}`;
        }
        else {
            method = 'GET';
            path = '/_component_template';
        }
        const meta = {
            name: 'cluster.get_component_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async getSettings(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.get_settings'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = '/_cluster/settings';
        const meta = {
            name: 'cluster.get_settings'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async health(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.health'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.index != null) {
            method = 'GET';
            path = `/_cluster/health/${encodeURIComponent(params.index.toString())}`;
        }
        else {
            method = 'GET';
            path = '/_cluster/health';
        }
        const meta = {
            name: 'cluster.health',
            pathParts: {
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async info(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.info'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = `/_info/${encodeURIComponent(params.target.toString())}`;
        const meta = {
            name: 'cluster.info',
            pathParts: {
                target: params.target
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async pendingTasks(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.pending_tasks'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = '/_cluster/pending_tasks';
        const meta = {
            name: 'cluster.pending_tasks'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async postVotingConfigExclusions(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.post_voting_config_exclusions'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'POST';
        const path = '/_cluster/voting_config_exclusions';
        const meta = {
            name: 'cluster.post_voting_config_exclusions'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putComponentTemplate(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['cluster.put_component_template'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = `/_component_template/${encodeURIComponent(params.name.toString())}`;
        const meta = {
            name: 'cluster.put_component_template',
            pathParts: {
                name: params.name
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async putSettings(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['cluster.put_settings'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'PUT';
        const path = '/_cluster/settings';
        const meta = {
            name: 'cluster.put_settings'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async remoteInfo(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.remote_info'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        const method = 'GET';
        const path = '/_remote/info';
        const meta = {
            name: 'cluster.remote_info'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async reroute(params, options) {
        const { path: acceptedPath, body: acceptedBody, query: acceptedQuery } = this.acceptedParams['cluster.reroute'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedBody.includes(key)) {
                body = body !== null && body !== void 0 ? body : {};
                // @ts-expect-error
                body[key] = params[key];
            }
            else if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                if (acceptedQuery.includes(key) || commonQueryParams.includes(key)) {
                    // @ts-expect-error
                    querystring[key] = params[key];
                }
                else {
                    body = body !== null && body !== void 0 ? body : {};
                    // @ts-expect-error
                    body[key] = params[key];
                }
            }
        }
        const method = 'POST';
        const path = '/_cluster/reroute';
        const meta = {
            name: 'cluster.reroute'
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async state(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.state'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.metric != null && params.index != null) {
            method = 'GET';
            path = `/_cluster/state/${encodeURIComponent(params.metric.toString())}/${encodeURIComponent(params.index.toString())}`;
        }
        else if (params.metric != null) {
            method = 'GET';
            path = `/_cluster/state/${encodeURIComponent(params.metric.toString())}`;
        }
        else {
            method = 'GET';
            path = '/_cluster/state';
        }
        const meta = {
            name: 'cluster.state',
            pathParts: {
                metric: params.metric,
                index: params.index
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
    async stats(params, options) {
        const { path: acceptedPath } = this.acceptedParams['cluster.stats'];
        const userQuery = params === null || params === void 0 ? void 0 : params.querystring;
        const querystring = userQuery != null ? { ...userQuery } : {};
        let body;
        const userBody = params === null || params === void 0 ? void 0 : params.body;
        if (userBody != null) {
            if (typeof userBody === 'string') {
                body = userBody;
            }
            else {
                body = { ...userBody };
            }
        }
        params = params !== null && params !== void 0 ? params : {};
        for (const key in params) {
            if (acceptedPath.includes(key)) {
                continue;
            }
            else if (key !== 'body' && key !== 'querystring') {
                // @ts-expect-error
                querystring[key] = params[key];
            }
        }
        let method = '';
        let path = '';
        if (params.node_id != null) {
            method = 'GET';
            path = `/_cluster/stats/nodes/${encodeURIComponent(params.node_id.toString())}`;
        }
        else {
            method = 'GET';
            path = '/_cluster/stats';
        }
        const meta = {
            name: 'cluster.stats',
            pathParts: {
                node_id: params.node_id
            }
        };
        return await this.transport.request({ path, method, querystring, body, meta }, options);
    }
}
exports.default = Cluster;
//# sourceMappingURL=cluster.js.map