---
# yaml-language-server: $schema=https://json.schemastore.org/catalog-info.json
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: elasticsearch-js
spec:
  type: library
  owner: group:devtools-team
  lifecycle: production

---
# yaml-language-server: $schema=https://gist.githubusercontent.com/elasticmachine/988b80dae436cafea07d9a4a460a011d/raw/e57ee3bed7a6f73077a3f55a38e76e40ec87a7cf/rre.schema.json
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  name: elasticsearch-js-integration-tests
  description: elasticsearch-js - integration tests
spec:
  type: buildkite-pipeline
  owner: group:devtools-team
  system: buildkite
  implementation:
    apiVersion: buildkite.elastic.dev/v1
    kind: Pipeline
    metadata:
      name: elasticsearch-js - integration tests
    spec:
      repository: elastic/elasticsearch-js
      pipeline_file: .buildkite/pipeline.yml
      env:
        ELASTIC_SLACK_NOTIFICATIONS_ENABLED: "true"
        SLACK_NOTIFICATIONS_CHANNEL: "#devtools-notify-javascript"
      teams:
        devtools-team:
          access_level: MANAGE_BUILD_AND_READ
        everyone:
          access_level: READ_ONLY
      provider_settings:
        build_pull_requests: false
        build_branches: false
      cancel_intermediate_builds: true
      cancel_intermediate_builds_branch_filter: "!main"
      schedules:
        main:
          branch: "main"
          cronline: "@daily"
        8_x:
          branch: "8.x"
          cronline: "@daily"
        8_17:
          branch: "8.17"
          cronline: "@daily"
        8_18:
          branch: "8.18"
          cronline: "@daily"
